"use client"

import { Monitor, Smartphone, Wifi, MessageSquare, Shield, Plus } from "lucide-react"

interface Device {
  id: string
  name: string
  type: "desktop" | "laptop" | "mobile"
  user: string
  avatar: string
  ip: string
  status: "online" | "away" | "editing"
  role: "owner" | "collaborator" | "viewer"
  lastSeen: string
  currentProject?: string
}

interface DevicesPageProps {
  onDeviceChat: (device: Device) => void
}

const CONNECTED_DEVICES: Device[] = [
  {
    id: "1",
    name: "MacBook Pro",
    type: "laptop",
    user: "<PERSON>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    ip: "*************",
    status: "editing",
    role: "owner",
    lastSeen: "now",
    currentProject: "E-commerce Platform",
  },
  {
    id: "2",
    name: "Windows Desktop",
    type: "desktop",
    user: "<PERSON>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
    ip: "*************",
    status: "online",
    role: "collaborator",
    lastSeen: "2 min ago",
  },
  {
    id: "3",
    name: "iPad Pro",
    type: "mobile",
    user: "Mike Johnson",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    ip: "*************",
    status: "away",
    role: "viewer",
    lastSeen: "15 min ago",
  },
]

export default function DevicesPage({ onDeviceChat }: DevicesPageProps) {
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case "desktop":
        return Monitor
      case "laptop":
        return Monitor
      case "mobile":
        return Smartphone
      default:
        return Monitor
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "editing":
        return "bg-emerald-500"
      case "away":
        return "bg-yellow-500"
      default:
        return "bg-gray-400"
    }
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "owner":
        return "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300"
      case "collaborator":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
      case "viewer":
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
    }
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Connected Devices</h2>
        <p className="text-gray-600 dark:text-gray-400">Manage devices and collaborators on your local network</p>
      </div>

      {/* Network Status */}
      <div className="mb-8 p-6 rounded-2xl bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
            <Wifi className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">Local Network Active</h3>
            <p className="text-green-700 dark:text-green-300">
              Broadcasting on flow.local:8080 • {CONNECTED_DEVICES.length} devices connected
            </p>
          </div>
        </div>
      </div>

      {/* Devices Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {CONNECTED_DEVICES.map((device) => {
          const DeviceIcon = getDeviceIcon(device.type)
          return (
            <div
              key={device.id}
              className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg hover:border-green-300 dark:hover:border-green-600 transition-all"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <img
                      src={device.avatar || "/placeholder.svg"}
                      alt={device.user}
                      className="w-12 h-12 rounded-xl object-cover"
                    />
                    <div className="absolute -bottom-1 -right-1 flex items-center justify-center">
                      <div
                        className={`w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${getStatusColor(device.status)}`}
                      />
                      <div
                        className={`absolute w-4 h-4 rounded-full ${getStatusColor(device.status)} animate-ping opacity-20`}
                      />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">{device.user}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{device.name}</p>
                  </div>
                </div>
                <span className={`px-3 py-1 text-xs rounded-lg font-medium ${getRoleBadgeColor(device.role)}`}>
                  {device.role}
                </span>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <DeviceIcon className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">{device.ip}</span>
                </div>

                <div className="flex items-center gap-2">
                  <div className="relative flex items-center">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(device.status)}`} />
                    <div
                      className={`absolute w-3 h-3 rounded-full ${getStatusColor(device.status)} animate-ping opacity-30`}
                    />
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">{device.status}</span>
                  <span className="text-sm text-gray-400">• {device.lastSeen}</span>
                </div>

                {device.currentProject && (
                  <div className="p-3 rounded-lg bg-green-50 dark:bg-green-900/20">
                    <p className="text-sm font-medium text-green-700 dark:text-green-300">Currently editing:</p>
                    <p className="text-sm text-green-600 dark:text-green-400">{device.currentProject}</p>
                  </div>
                )}
              </div>

              <div className="flex gap-2 mt-4">
                <button
                  onClick={() => onDeviceChat(device)}
                  className="flex-1 py-2 px-3 rounded-lg bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors flex items-center justify-center gap-2"
                >
                  <MessageSquare className="w-4 h-4" />
                  Chat
                </button>
                <button className="flex-1 py-2 px-3 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center">
                  <Shield className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                </button>
              </div>
            </div>
          )
        })}

        {/* Add Device Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl border-2 border-dashed border-gray-300 dark:border-gray-600 p-6 flex flex-col items-center justify-center text-center hover:border-green-500 dark:hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/10 transition-all cursor-pointer">
          <div className="w-12 h-12 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center mb-3">
            <Plus className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <h3 className="font-medium text-gray-900 dark:text-white mb-1">Invite Device</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">Scan for new devices</p>
        </div>
      </div>

      {/* Network Settings */}
      <div className="mt-8 bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Network Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Local IP</p>
            <p className="text-lg font-mono text-gray-900 dark:text-white">*************</p>
          </div>
          <div className="p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Network Name</p>
            <p className="text-lg font-mono text-gray-900 dark:text-white">flow.local</p>
          </div>
          <div className="p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Discovery Port</p>
            <p className="text-lg font-mono text-gray-900 dark:text-white">8080</p>
          </div>
        </div>
      </div>
    </div>
  )
}
