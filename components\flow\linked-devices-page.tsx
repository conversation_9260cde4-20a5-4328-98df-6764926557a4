"use client"

import { useState } from "react"
import { Wifi, Upload, Download, CheckCircle, Clock, X, FolderOpen } from "lucide-react"
import { toast } from "./toast"

interface MobileDevice {
  id: string
  name: string
  type: "iPhone" | "Android"
  user: string
  avatar: string
  status: "connected" | "disconnected" | "connecting"
  batteryLevel: number
  lastSeen: string
}

interface FileTransfer {
  id: string
  fileName: string
  fileSize: string
  deviceId: string
  deviceName: string
  progress: number
  status: "pending" | "transferring" | "completed" | "failed"
  direction: "upload" | "download"
}

const MOBILE_DEVICES: MobileDevice[] = [
  {
    id: "1",
    name: "<PERSON>'s iPhone",
    type: "iPhone",
    user: "<PERSON>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    status: "connected",
    batteryLevel: 85,
    lastSeen: "now",
  },
  {
    id: "2",
    name: "<PERSON>'s Galaxy",
    type: "Android",
    user: "<PERSON>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
    status: "disconnected",
    batteryLevel: 42,
    lastSeen: "5 min ago",
  },
  {
    id: "3",
    name: "Mike's Pixel",
    type: "Android",
    user: "Mike Johnson",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    status: "connecting",
    batteryLevel: 67,
    lastSeen: "2 min ago",
  },
]

export default function LinkedDevicesPage() {
  const [devices, setDevices] = useState(MOBILE_DEVICES)
  const [fileTransfers, setFileTransfers] = useState<FileTransfer[]>([
    {
      id: "1",
      fileName: "project-assets.zip",
      fileSize: "24.5 MB",
      deviceId: "1",
      deviceName: "Alex's iPhone",
      progress: 75,
      status: "transferring",
      direction: "upload",
    },
    {
      id: "2",
      fileName: "design-mockups.pdf",
      fileSize: "8.2 MB",
      deviceId: "1",
      deviceName: "Alex's iPhone",
      progress: 100,
      status: "completed",
      direction: "download",
    },
  ])
  const [showFileSelector, setShowFileSelector] = useState(false)
  const [selectedDevice, setSelectedDevice] = useState<MobileDevice | null>(null)

  const handleConnect = (deviceId: string) => {
    setDevices(
      devices.map((d) =>
        d.id === deviceId ? { ...d, status: d.status === "connected" ? "disconnected" : "connecting" } : d,
      ),
    )

    setTimeout(() => {
      setDevices(
        devices.map((d) =>
          d.id === deviceId && d.status === "connecting" ? { ...d, status: "connected", lastSeen: "now" } : d,
        ),
      )
      toast.success("Device connected successfully")
    }, 2000)
  }

  const handleFileSelect = (device: MobileDevice) => {
    setSelectedDevice(device)
    setShowFileSelector(true)
  }

  const handleFileTransfer = (fileName: string) => {
    if (!selectedDevice) return

    const newTransfer: FileTransfer = {
      id: Date.now().toString(),
      fileName,
      fileSize: `${Math.floor(Math.random() * 50) + 1}.${Math.floor(Math.random() * 9) + 1} MB`,
      deviceId: selectedDevice.id,
      deviceName: selectedDevice.name,
      progress: 0,
      status: "transferring",
      direction: "upload",
    }

    setFileTransfers([...fileTransfers, newTransfer])
    setShowFileSelector(false)
    setSelectedDevice(null)

    // Simulate file transfer progress
    const interval = setInterval(() => {
      setFileTransfers((prev) =>
        prev.map((transfer) => {
          if (transfer.id === newTransfer.id && transfer.progress < 100) {
            const newProgress = Math.min(transfer.progress + Math.random() * 15, 100)
            return {
              ...transfer,
              progress: newProgress,
              status: newProgress === 100 ? "completed" : "transferring",
            }
          }
          return transfer
        }),
      )
    }, 500)

    setTimeout(() => {
      clearInterval(interval)
      toast.success(`File "${fileName}" transferred successfully`)
    }, 8000)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "connected":
        return "bg-green-500"
      case "connecting":
        return "bg-yellow-500"
      case "disconnected":
        return "bg-gray-400"
      default:
        return "bg-gray-400"
    }
  }

  const getDeviceIcon = (type: string) => {
    return type === "iPhone" ? "📱" : "📱"
  }

  const getBatteryColor = (level: number) => {
    if (level > 50) return "bg-green-500"
    if (level > 20) return "bg-yellow-500"
    return "bg-red-500"
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Linked Devices</h2>
        <p className="text-gray-600 dark:text-gray-400">Connect mobile devices and transfer files seamlessly</p>
      </div>

      {/* Connection Status */}
      <div className="mb-8 p-6 rounded-2xl bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
            <Wifi className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">Device Discovery Active</h3>
            <p className="text-blue-700 dark:text-blue-300">
              Scanning for mobile devices • {devices.filter((d) => d.status === "connected").length} connected
            </p>
          </div>
        </div>
      </div>

      {/* Mobile Devices Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {devices.map((device) => (
          <div
            key={device.id}
            className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="text-3xl">{getDeviceIcon(device.type)}</div>
                  <div className="absolute -bottom-1 -right-1 flex items-center justify-center">
                    <div
                      className={`w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${getStatusColor(device.status)}`}
                    />
                    {device.status === "connecting" && (
                      <div className="absolute w-4 h-4 rounded-full bg-yellow-500 animate-ping opacity-30" />
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">{device.name}</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{device.user}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-1 mb-1">
                  <div className={`w-6 h-3 rounded-sm border border-gray-300 dark:border-gray-600 relative`}>
                    <div
                      className={`h-full rounded-sm ${getBatteryColor(device.batteryLevel)}`}
                      style={{ width: `${device.batteryLevel}%` }}
                    />
                  </div>
                  <span className="text-xs text-gray-500">{device.batteryLevel}%</span>
                </div>
                <span className="text-xs text-gray-400">{device.lastSeen}</span>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="relative flex items-center">
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(device.status)}`} />
                  {device.status === "connecting" && (
                    <div className="absolute w-3 h-3 rounded-full bg-yellow-500 animate-ping opacity-30" />
                  )}
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                  {device.status === "connecting" ? "Connecting..." : device.status}
                </span>
              </div>

              <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Device Type</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">{device.type}</p>
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <button
                onClick={() => handleConnect(device.id)}
                disabled={device.status === "connecting"}
                className={`flex-1 py-2 px-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${
                  device.status === "connected"
                    ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-900/50"
                    : device.status === "connecting"
                      ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 cursor-not-allowed"
                      : "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50"
                }`}
              >
                {device.status === "connecting" && <Clock className="w-4 h-4 animate-spin" />}
                {device.status === "connected"
                  ? "Disconnect"
                  : device.status === "connecting"
                    ? "Connecting"
                    : "Connect"}
              </button>
              <button
                onClick={() => handleFileSelect(device)}
                disabled={device.status !== "connected"}
                className="flex-1 py-2 px-3 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Upload className="w-4 h-4" />
                Send Files
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* File Transfers */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">File Transfers</h3>
          <span className="px-3 py-1 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium">
            {fileTransfers.filter((t) => t.status === "transferring").length} Active
          </span>
        </div>

        {fileTransfers.length === 0 ? (
          <div className="text-center py-12">
            <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No file transfers</h4>
            <p className="text-gray-500 dark:text-gray-400">Connect a device and start transferring files</p>
          </div>
        ) : (
          <div className="space-y-4">
            {fileTransfers.map((transfer) => (
              <div
                key={transfer.id}
                className="border border-gray-200 dark:border-gray-600 rounded-xl p-4 hover:shadow-sm transition-all"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        transfer.direction === "upload"
                          ? "bg-green-100 dark:bg-green-900/30"
                          : "bg-blue-100 dark:bg-blue-900/30"
                      }`}
                    >
                      {transfer.direction === "upload" ? (
                        <Upload
                          className={`w-5 h-5 ${
                            transfer.direction === "upload"
                              ? "text-green-600 dark:text-green-400"
                              : "text-blue-600 dark:text-blue-400"
                          }`}
                        />
                      ) : (
                        <Download className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{transfer.fileName}</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {transfer.fileSize} • {transfer.deviceName}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {transfer.status === "completed" && <CheckCircle className="w-5 h-5 text-green-500" />}
                    {transfer.status === "transferring" && <Clock className="w-5 h-5 text-blue-500 animate-spin" />}
                    {transfer.status === "failed" && <X className="w-5 h-5 text-red-500" />}
                    <span
                      className={`text-sm font-medium ${
                        transfer.status === "completed"
                          ? "text-green-600 dark:text-green-400"
                          : transfer.status === "transferring"
                            ? "text-blue-600 dark:text-blue-400"
                            : transfer.status === "failed"
                              ? "text-red-600 dark:text-red-400"
                              : "text-gray-600 dark:text-gray-400"
                      }`}
                    >
                      {transfer.status === "completed"
                        ? "Complete"
                        : transfer.status === "transferring"
                          ? `${Math.round(transfer.progress)}%`
                          : transfer.status === "failed"
                            ? "Failed"
                            : "Pending"}
                    </span>
                  </div>
                </div>

                {transfer.status === "transferring" && (
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${transfer.progress}%` }}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* File Selector Modal */}
      {showFileSelector && selectedDevice && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Send Files to {selectedDevice.name}
              </h3>
              <button
                onClick={() => setShowFileSelector(false)}
                className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>

            <div className="space-y-3">
              {["project-config.json", "assets.zip", "documentation.pdf", "screenshots.zip"].map((fileName) => (
                <button
                  key={fileName}
                  onClick={() => handleFileTransfer(fileName)}
                  className="w-full p-4 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/10 transition-all text-left"
                >
                  <div className="flex items-center gap-3">
                    <FolderOpen className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{fileName}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {Math.floor(Math.random() * 50) + 1}.{Math.floor(Math.random() * 9) + 1} MB
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={() => setShowFileSelector(false)}
                className="w-full py-3 px-4 rounded-xl border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
