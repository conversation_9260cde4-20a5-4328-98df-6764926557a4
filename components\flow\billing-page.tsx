"use client"

import { useState } from "react"
import { Download, Check, X, Users, Database, Bot, FileText } from "lucide-react"
import { toast } from "./toast"

export default function BillingPage() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly")
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null)

  const currentUsage = {
    projects: { current: 3, limit: 5 },
    storage: { current: 2.4, limit: 10 }, // GB
    teamMembers: { current: 2, limit: 3 },
    aiRequests: { current: 847, limit: 1000 },
  }

  const plans = [
    {
      id: "free",
      name: "Free",
      description: "Perfect for getting started",
      price: { monthly: 0, yearly: 0 },
      features: [
        "5 projects",
        "10GB storage",
        "3 team members",
        "1,000 AI requests/month",
        "Basic support",
        "Community access",
      ],
      limitations: ["No custom domains", "Limited integrations", "Basic analytics"],
      current: true,
    },
    {
      id: "pro",
      name: "Pro",
      description: "For growing teams and projects",
      price: { monthly: 19, yearly: 15.2 }, // 20% discount yearly
      features: [
        "Unlimited projects",
        "100GB storage",
        "10 team members",
        "10,000 AI requests/month",
        "Priority support",
        "Advanced analytics",
        "Custom domains",
        "All integrations",
        "Version control",
        "Team collaboration tools",
      ],
      limitations: [],
      popular: true,
    },
    {
      id: "team",
      name: "Team",
      description: "For large teams and enterprises",
      price: { monthly: 49, yearly: 39.2 }, // 20% discount yearly
      features: [
        "Everything in Pro",
        "500GB storage",
        "Unlimited team members",
        "50,000 AI requests/month",
        "24/7 dedicated support",
        "Advanced security",
        "SSO integration",
        "Custom branding",
        "API access",
        "Advanced permissions",
        "Audit logs",
      ],
      limitations: [],
    },
  ]

  const billingHistory = [
    {
      id: "1",
      date: "2024-01-01",
      description: "Pro Plan - Monthly",
      amount: 19.0,
      status: "paid",
      invoice: "INV-2024-001",
    },
    {
      id: "2",
      date: "2023-12-01",
      description: "Pro Plan - Monthly",
      amount: 19.0,
      status: "paid",
      invoice: "INV-2023-012",
    },
    {
      id: "3",
      date: "2023-11-01",
      description: "Pro Plan - Monthly",
      amount: 19.0,
      status: "paid",
      invoice: "INV-2023-011",
    },
  ]

  const handleUpgrade = (planId: string) => {
    setSelectedPlan(planId)
    setShowPaymentModal(true)
  }

  const handlePayment = () => {
    setShowPaymentModal(false)
    toast.success("Payment successful! Your plan has been upgraded.")
  }

  const getUsagePercentage = (current: number, limit: number) => {
    return Math.min((current / limit) * 100, 100)
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return "bg-red-500"
    if (percentage >= 70) return "bg-yellow-500"
    return "bg-green-500"
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Billing & Usage</h2>
        <p className="text-gray-600 dark:text-gray-400">Manage your subscription and monitor usage</p>
      </div>

      {/* Current Usage */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Projects</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {currentUsage.projects.current} of {currentUsage.projects.limit}
                </p>
              </div>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(currentUsage.projects.current, currentUsage.projects.limit))}`}
              style={{ width: `${getUsagePercentage(currentUsage.projects.current, currentUsage.projects.limit)}%` }}
            />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <Database className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Storage</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {currentUsage.storage.current}GB of {currentUsage.storage.limit}GB
                </p>
              </div>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(currentUsage.storage.current, currentUsage.storage.limit))}`}
              style={{ width: `${getUsagePercentage(currentUsage.storage.current, currentUsage.storage.limit)}%` }}
            />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Team Members</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {currentUsage.teamMembers.current} of {currentUsage.teamMembers.limit}
                </p>
              </div>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(currentUsage.teamMembers.current, currentUsage.teamMembers.limit))}`}
              style={{
                width: `${getUsagePercentage(currentUsage.teamMembers.current, currentUsage.teamMembers.limit)}%`,
              }}
            />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                <Bot className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">AI Requests</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {currentUsage.aiRequests.current.toLocaleString()} of {currentUsage.aiRequests.limit.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getUsageColor(getUsagePercentage(currentUsage.aiRequests.current, currentUsage.aiRequests.limit))}`}
              style={{
                width: `${getUsagePercentage(currentUsage.aiRequests.current, currentUsage.aiRequests.limit)}%`,
              }}
            />
          </div>
        </div>
      </div>

      {/* Billing Cycle Toggle */}
      <div className="flex items-center justify-center mb-8">
        <div className="bg-gray-100 dark:bg-gray-800 rounded-xl p-1 flex">
          <button
            onClick={() => setBillingCycle("monthly")}
            className={`px-6 py-2 rounded-lg font-medium transition-colors ${
              billingCycle === "monthly"
                ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm"
                : "text-gray-600 dark:text-gray-400"
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setBillingCycle("yearly")}
            className={`px-6 py-2 rounded-lg font-medium transition-colors relative ${
              billingCycle === "yearly"
                ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm"
                : "text-gray-600 dark:text-gray-400"
            }`}
          >
            Yearly
            <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-0.5 rounded-full">
              20% off
            </span>
          </button>
        </div>
      </div>

      {/* Pricing Plans */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        {plans.map((plan) => (
          <div
            key={plan.id}
            className={`relative bg-white dark:bg-gray-800 rounded-2xl border-2 p-8 ${
              plan.popular
                ? "border-green-500 shadow-lg scale-105"
                : plan.current
                  ? "border-blue-500"
                  : "border-gray-200 dark:border-gray-700"
            }`}
          >
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
              </div>
            )}

            {plan.current && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">Current Plan</span>
              </div>
            )}

            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{plan.name}</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">{plan.description}</p>
              <div className="flex items-baseline justify-center gap-1">
                <span className="text-4xl font-bold text-gray-900 dark:text-white">${plan.price[billingCycle]}</span>
                <span className="text-gray-600 dark:text-gray-400">
                  /{billingCycle === "monthly" ? "month" : "year"}
                </span>
              </div>
              {billingCycle === "yearly" && plan.price.yearly > 0 && (
                <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                  Save ${(plan.price.monthly * 12 - plan.price.yearly * 12).toFixed(0)} per year
                </p>
              )}
            </div>

            <div className="space-y-4 mb-8">
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                </div>
              ))}
              {plan.limitations.map((limitation, index) => (
                <div key={index} className="flex items-center gap-3">
                  <X className="w-5 h-5 text-red-500 flex-shrink-0" />
                  <span className="text-gray-500 dark:text-gray-400">{limitation}</span>
                </div>
              ))}
            </div>

            <button
              onClick={() => (plan.current ? null : handleUpgrade(plan.id))}
              disabled={plan.current}
              className={`w-full py-3 px-4 rounded-xl font-medium transition-colors ${
                plan.current
                  ? "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  : plan.popular
                    ? "bg-green-600 text-white hover:bg-green-700"
                    : "bg-gray-900 dark:bg-white text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100"
              }`}
            >
              {plan.current ? "Current Plan" : `Upgrade to ${plan.name}`}
            </button>
          </div>
        ))}
      </div>

      {/* Billing History */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Billing History</h3>
          <button className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            <Download className="w-4 h-4" />
            Export All
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Date</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Description</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Amount</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Invoice</th>
              </tr>
            </thead>
            <tbody>
              {billingHistory.map((item) => (
                <tr key={item.id} className="border-b border-gray-100 dark:border-gray-700/50">
                  <td className="py-4 px-4 text-gray-700 dark:text-gray-300">
                    {new Date(item.date).toLocaleDateString()}
                  </td>
                  <td className="py-4 px-4 text-gray-700 dark:text-gray-300">{item.description}</td>
                  <td className="py-4 px-4 text-gray-900 dark:text-white font-medium">${item.amount.toFixed(2)}</td>
                  <td className="py-4 px-4">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                      {item.status}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <button className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 font-medium">
                      {item.invoice}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-8">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Upgrade to {plans.find((p) => p.id === selectedPlan)?.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                ${plans.find((p) => p.id === selectedPlan)?.price[billingCycle]}/
                {billingCycle === "monthly" ? "month" : "year"}
              </p>
            </div>

            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Card Number</label>
                <input
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Expiry Date</label>
                  <input
                    type="text"
                    placeholder="MM/YY"
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">CVC</label>
                  <input
                    type="text"
                    placeholder="123"
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Cardholder Name
                </label>
                <input
                  type="text"
                  placeholder="John Doe"
                  className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                />
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowPaymentModal(false)}
                  className="flex-1 py-3 px-4 rounded-xl border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handlePayment}
                  className="flex-1 py-3 px-4 rounded-xl bg-green-600 text-white hover:bg-green-700 transition-colors"
                >
                  Pay Now
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
