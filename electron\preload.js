const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),

  // File system dialogs
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),

  // Menu event listeners
  onMenuNewProject: (callback) => ipcRenderer.on('menu-new-project', callback),
  onMenuOpenProject: (callback) => ipcRenderer.on('menu-open-project', callback),
  onMenuSettings: (callback) => ipcRenderer.on('menu-settings', callback),
  onMenuDiscoverDevices: (callback) => ipcRenderer.on('menu-discover-devices', callback),
  onMenuSharePort: (callback) => ipcRenderer.on('menu-share-port', callback),
  onMenuAIAssistant: (callback) => ipcRenderer.on('menu-ai-assistant', callback),

  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),

  // Platform info
  platform: process.platform,
  isElectron: true
});

// Expose a limited API for Flow-specific functionality
contextBridge.exposeInMainWorld('flowAPI', {
  // Project management
  createProject: (projectData) => ipcRenderer.invoke('create-project', projectData),
  openProject: (projectPath) => ipcRenderer.invoke('open-project', projectPath),
  saveProject: (projectData) => ipcRenderer.invoke('save-project', projectData),

  // File operations
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content),
  watchFile: (filePath, callback) => {
    ipcRenderer.on('file-changed', (event, changedPath) => {
      if (changedPath === filePath) {
        callback(changedPath);
      }
    });
  },

  // Network/P2P functionality (to be implemented)
  discoverPeers: () => ipcRenderer.invoke('discover-peers'),
  connectToPeer: (peerId) => ipcRenderer.invoke('connect-peer', peerId),
  sharePort: (port, options) => ipcRenderer.invoke('share-port', port, options),

  // AI functionality (to be implemented)
  queryAI: (query, context) => ipcRenderer.invoke('query-ai', query, context),
  indexProject: (projectPath) => ipcRenderer.invoke('index-project', projectPath),

  // Version control
  commitChanges: (message, files) => ipcRenderer.invoke('commit-changes', message, files),
  getVersionHistory: (projectPath) => ipcRenderer.invoke('get-version-history', projectPath),
  revertToVersion: (versionId) => ipcRenderer.invoke('revert-to-version', versionId),

  // Device sync
  syncWithDevice: (deviceId) => ipcRenderer.invoke('sync-with-device', deviceId),
  getConnectedDevices: () => ipcRenderer.invoke('get-connected-devices'),

  // Notifications
  showNotification: (title, body, options) => ipcRenderer.invoke('show-notification', title, body, options),

  // Event listeners for real-time updates
  onPeerConnected: (callback) => ipcRenderer.on('peer-connected', callback),
  onPeerDisconnected: (callback) => ipcRenderer.on('peer-disconnected', callback),
  onFileSync: (callback) => ipcRenderer.on('file-sync', callback),
  onProjectUpdate: (callback) => ipcRenderer.on('project-update', callback)
});
