const { build } = require('electron-builder');
const path = require('path');

async function buildDesktopApp() {
  try {
    console.log('Building Flow desktop application...');
    
    // Build configuration
    const config = {
      appId: 'com.flow.app',
      productName: 'Flow',
      directories: {
        output: 'dist'
      },
      files: [
        'out/**/*',
        'electron/**/*',
        'node_modules/**/*',
        'package.json'
      ],
      win: {
        target: [
          {
            target: 'nsis',
            arch: ['x64']
          }
        ],
        icon: 'electron/assets/icon.ico' // We'll create this
      },
      mac: {
        category: 'public.app-category.developer-tools',
        target: [
          {
            target: 'dmg',
            arch: ['x64', 'arm64']
          }
        ],
        icon: 'electron/assets/icon.icns'
      },
      linux: {
        target: [
          {
            target: 'AppImage',
            arch: ['x64']
          }
        ],
        icon: 'electron/assets/icon.png'
      },
      nsis: {
        oneClick: false,
        allowToChangeInstallationDirectory: true,
        createDesktopShortcut: true,
        createStartMenuShortcut: true
      }
    };

    // Build the app
    await build({
      config,
      publish: 'never'
    });

    console.log('✅ Desktop application built successfully!');
    console.log('📁 Output directory: dist/');
    
  } catch (error) {
    console.error('❌ Build failed:', error);
    process.exit(1);
  }
}

// Run the build
buildDesktopApp();
