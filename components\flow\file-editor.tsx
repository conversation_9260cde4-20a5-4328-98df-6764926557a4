"use client"

interface FileEditorProps {
  fileName: string
}

export default function FileEditor({ fileName }: FileEditorProps) {
  const getFileContent = (fileName: string) => {
    switch (fileName) {
      case "src/App.tsx":
        return `import React from 'react';
import './App.css';
import Header from './components/Header';
import Sidebar from './components/Sidebar';

function App() {
  return (
    <div className="App">
      <Header />
      <div className="app-content">
        <Sidebar />
        <main className="main-content">
          <h1>Welcome to Flow</h1>
          <p>Local-first developer collaboration tool</p>
        </main>
      </div>
    </div>
  );
}

export default App;`
      case "src/components/Header.tsx":
        return `import React from 'react';

interface HeaderProps {
  title?: string;
}

const Header: React.FC<HeaderProps> = ({ title = 'Flow' }) => {
  return (
    <header className="header">
      <div className="header-content">
        <h1>{title}</h1>
        <nav>
          <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#projects">Projects</a></li>
            <li><a href="#settings">Settings</a></li>
          </ul>
        </nav>
      </div>
    </header>
  );
};

export default Header;`
      default:
        return `// ${fileName}
// File content will be loaded here...

export default function Component() {
  return (
    <div>
      <h1>Hello from ${fileName}</h1>
    </div>
  );
}`
    }
  }

  return (
    <div className="h-full bg-white dark:bg-gray-900 font-mono">
      <div className="h-full p-4">
        <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
          <code>{getFileContent(fileName)}</code>
        </pre>
      </div>
    </div>
  )
}
