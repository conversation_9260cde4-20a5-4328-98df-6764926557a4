"use client"

import { useState } from "react"
import { X, Mail, Crown, Shield, Edit, Eye, UserPlus } from "lucide-react"
import { toast } from "./toast"

interface Collaborator {
  id: string
  name: string
  email: string
  avatar: string
  role: "owner" | "admin" | "editor" | "viewer"
  status: "active" | "pending" | "inactive"
  joinedAt: string
}

interface CollaboratorPanelProps {
  onClose: () => void
}

const COLLABORATORS: Collaborator[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    role: "owner",
    status: "active",
    joinedAt: "2024-01-15",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
    role: "admin",
    status: "active",
    joinedAt: "2024-01-20",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    role: "editor",
    status: "active",
    joinedAt: "2024-02-01",
  },
  {
    id: "4",
    name: "Emma Wilson",
    email: "<EMAIL>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
    role: "viewer",
    status: "pending",
    joinedAt: "2024-02-10",
  },
]

export default function CollaboratorPanel({ onClose }: CollaboratorPanelProps) {
  const [collaborators, setCollaborators] = useState(COLLABORATORS)
  const [showInviteModal, setShowInviteModal] = useState(false)
  const [inviteEmail, setInviteEmail] = useState("")
  const [inviteRole, setInviteRole] = useState<"admin" | "editor" | "viewer">("editor")

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "owner":
        return <Crown className="w-4 h-4 text-yellow-500" />
      case "admin":
        return <Shield className="w-4 h-4 text-red-500" />
      case "editor":
        return <Edit className="w-4 h-4 text-blue-500" />
      case "viewer":
        return <Eye className="w-4 h-4 text-gray-500" />
      default:
        return null
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case "owner":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300"
      case "admin":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300"
      case "editor":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
      case "viewer":
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
      case "pending":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300"
      case "inactive":
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
    }
  }

  const handleRoleChange = (collaboratorId: string, newRole: "admin" | "editor" | "viewer") => {
    setCollaborators((prev) =>
      prev.map((collab) => (collab.id === collaboratorId ? { ...collab, role: newRole } : collab)),
    )
    toast.success("Role updated successfully")
  }

  const handleRemoveCollaborator = (collaboratorId: string) => {
    setCollaborators((prev) => prev.filter((collab) => collab.id !== collaboratorId))
    toast.success("Collaborator removed")
  }

  const handleInvite = () => {
    if (!inviteEmail) {
      toast.error("Please enter an email address")
      return
    }

    const newCollaborator: Collaborator = {
      id: Date.now().toString(),
      name: inviteEmail.split("@")[0],
      email: inviteEmail,
      avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
      role: inviteRole,
      status: "pending",
      joinedAt: new Date().toISOString().split("T")[0],
    }

    setCollaborators((prev) => [...prev, newCollaborator])
    setShowInviteModal(false)
    setInviteEmail("")
    setInviteRole("editor")
    toast.success("Invitation sent successfully")
  }

  const rolePermissions = {
    owner: ["Full access", "Manage team", "Delete project", "Billing access"],
    admin: ["Edit project", "Manage collaborators", "Deploy project", "View analytics"],
    editor: ["Edit files", "Create branches", "Submit changes", "View project"],
    viewer: ["View files", "Download project", "Leave comments"],
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Team Collaborators</h2>
            <p className="text-gray-600 dark:text-gray-400">Manage team members and their permissions</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={() => setShowInviteModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors"
            >
              <UserPlus className="w-4 h-4" />
              Invite Member
            </button>
            <button
              onClick={onClose}
              className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Collaborators List */}
          <div className="space-y-4">
            {collaborators.map((collaborator) => (
              <div
                key={collaborator.id}
                className="flex items-center justify-between p-4 rounded-xl border border-gray-200 dark:border-gray-600 hover:shadow-sm transition-all"
              >
                <div className="flex items-center gap-4">
                  <img
                    src={collaborator.avatar || "/placeholder.svg"}
                    alt={collaborator.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-gray-900 dark:text-white">{collaborator.name}</h3>
                      {getRoleIcon(collaborator.role)}
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{collaborator.email}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getRoleColor(collaborator.role)}`}>
                        {collaborator.role}
                      </span>
                      <span
                        className={`px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(collaborator.status)}`}
                      >
                        {collaborator.status}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  {collaborator.role !== "owner" && (
                    <>
                      <select
                        value={collaborator.role}
                        onChange={(e) =>
                          handleRoleChange(collaborator.id, e.target.value as "admin" | "editor" | "viewer")
                        }
                        className="px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                      >
                        <option value="admin">Admin</option>
                        <option value="editor">Editor</option>
                        <option value="viewer">Viewer</option>
                      </select>
                      <button
                        onClick={() => handleRemoveCollaborator(collaborator.id)}
                        className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Role Permissions */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Role Permissions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Object.entries(rolePermissions).map(([role, permissions]) => (
                <div key={role} className="p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50">
                  <div className="flex items-center gap-2 mb-3">
                    {getRoleIcon(role)}
                    <h4 className="font-medium text-gray-900 dark:text-white capitalize">{role}</h4>
                  </div>
                  <ul className="space-y-2">
                    {permissions.map((permission, index) => (
                      <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        {permission}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Invite Modal */}
        {showInviteModal && (
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
            <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Invite Team Member</h3>
                <button
                  onClick={() => setShowInviteModal(false)}
                  className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      className="w-full pl-10 pr-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Role</label>
                  <select
                    value={inviteRole}
                    onChange={(e) => setInviteRole(e.target.value as "admin" | "editor" | "viewer")}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="admin">Admin - Full project access</option>
                    <option value="editor">Editor - Can edit and collaborate</option>
                    <option value="viewer">Viewer - Read-only access</option>
                  </select>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={() => setShowInviteModal(false)}
                    className="flex-1 py-3 px-4 rounded-xl border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleInvite}
                    className="flex-1 py-3 px-4 rounded-xl bg-green-600 text-white hover:bg-green-700 transition-colors"
                  >
                    Send Invite
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
