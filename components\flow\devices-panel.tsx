"use client"

import { X, Wifi, Monitor, Smartphone, Users, MessageSquare, Shield } from "lucide-react"

interface Device {
  id: string
  name: string
  type: "desktop" | "laptop" | "mobile"
  user: string
  avatar: string
  ip: string
  status: "online" | "away" | "editing"
  role: "owner" | "collaborator" | "viewer"
  lastSeen: string
}

const CONNECTED_DEVICES: Device[] = [
  {
    id: "1",
    name: "MacBook Pro",
    type: "laptop",
    user: "<PERSON>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    ip: "*************",
    status: "editing",
    role: "owner",
    lastSeen: "now",
  },
  {
    id: "2",
    name: "Windows Desktop",
    type: "desktop",
    user: "<PERSON>",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
    ip: "*************",
    status: "online",
    role: "collaborator",
    lastSeen: "2 min ago",
  },
  {
    id: "3",
    name: "iPad Pro",
    type: "mobile",
    user: "Mike Johnson",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
    ip: "*************",
    status: "away",
    role: "viewer",
    lastSeen: "15 min ago",
  },
]

interface DevicesPanelProps {
  onClose: () => void
}

export default function DevicesPanel({ onClose }: DevicesPanelProps) {
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case "desktop":
        return Monitor
      case "laptop":
        return Monitor
      case "mobile":
        return Smartphone
      default:
        return Monitor
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "editing":
        return "bg-emerald-500"
      case "away":
        return "bg-yellow-500"
      default:
        return "bg-gray-400"
    }
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "owner":
        return "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300"
      case "collaborator":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"
      case "viewer":
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-2xl bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center">
              <Users className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Connected Devices</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">{CONNECTED_DEVICES.length} devices on network</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Network Status */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3 p-4 rounded-2xl bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800">
            <Wifi className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            <div>
              <p className="text-sm font-medium text-emerald-900 dark:text-emerald-100">Local Network Active</p>
              <p className="text-xs text-emerald-700 dark:text-emerald-300">Broadcasting on flow.local:8080</p>
            </div>
          </div>
        </div>

        {/* Devices List */}
        <div className="p-6 space-y-4 max-h-96 overflow-y-auto">
          {CONNECTED_DEVICES.map((device) => {
            const DeviceIcon = getDeviceIcon(device.type)
            return (
              <div
                key={device.id}
                className="flex items-center justify-between p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <img
                      src={device.avatar || "/placeholder.svg"}
                      alt={device.user}
                      className="w-12 h-12 rounded-xl object-cover"
                    />
                    <div
                      className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${getStatusColor(device.status)}`}
                    />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <DeviceIcon className="w-4 h-4 text-gray-500" />
                      <h3 className="font-medium text-gray-900 dark:text-white">{device.name}</h3>
                      <span className={`px-2 py-1 text-xs rounded-lg font-medium ${getRoleBadgeColor(device.role)}`}>
                        {device.role}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{device.user}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      {device.ip} • {device.lastSeen}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <button className="p-2 rounded-xl bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors">
                    <MessageSquare className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                  </button>
                  <button className="p-2 rounded-xl bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors">
                    <Shield className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                  </button>
                </div>
              </div>
            )
          })}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <button className="w-full py-3 px-4 rounded-2xl bg-gradient-to-r from-emerald-500 to-green-600 text-white font-medium hover:from-emerald-600 hover:to-green-700 transition-all shadow-lg">
            Invite New Collaborator
          </button>
        </div>
      </div>
    </div>
  )
}
