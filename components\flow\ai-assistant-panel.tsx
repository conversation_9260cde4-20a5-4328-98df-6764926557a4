"use client"

import { useState, useRef, useEffect } from "react"
import { X, Send, Bot, User, Lightbulb, Code, TestTube, FileText, Sparkles, Zap, Brain } from "lucide-react"

interface Message {
  id: string
  type: "user" | "assistant"
  content: string
  timestamp: string
  suggestions?: string[]
}

interface AIAssistantPanelProps {
  onClose: () => void
  isCollapsed?: boolean
}

export default function AIAssistantPanel({ onClose, isCollapsed = false }: AIAssistantPanelProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "assistant",
      content:
        "👋 Hey there! I'm your AI coding companion. I can help you with code explanations, suggestions, debugging, refactoring, and more. What would you like to work on today?",
      timestamp: new Date().toISOString(),
      suggestions: ["Explain this code", "Suggest improvements", "Generate tests", "Add documentation"],
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date().toISOString(),
    }

    setMessages((prev) => [...prev, userMessage])
    setInputValue("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: generateAIResponse(inputValue),
        timestamp: new Date().toISOString(),
        suggestions: ["Show me an example", "Explain further", "Generate code", "What about edge cases?"],
      }
      setMessages((prev) => [...prev, aiResponse])
      setIsLoading(false)
    }, 1500)
  }

  const generateAIResponse = (input: string): string => {
    const responses = [
      "Great question! 🎯 Here's what I suggest:\n\n✨ **Optimization Tips:**\n• Consider using `useMemo` for expensive calculations\n• Implement proper error boundaries\n• Add TypeScript types for better type safety\n\n💡 Would you like me to show you a specific example?",
      "I can help you with that! 🚀 Here's a better approach:\n\n```typescript\nconst optimizedFunction = useCallback(() => {\n  // Your optimized logic here\n  return processData(data);\n}, [data]);\n```\n\n🔥 This will improve performance and prevent unnecessary re-renders!",
      "Excellent! 💪 Here are some best practices:\n\n🎨 **UI/UX Improvements:**\n• Use semantic HTML elements\n• Implement proper accessibility (ARIA labels)\n• Consider mobile-first responsive design\n• Add loading states and error handling\n\n✨ Want me to generate a complete example?",
      "Perfect timing! 🎉 Let me break this down:\n\n🔍 **Code Analysis:**\n• Your current implementation is solid\n• Consider extracting reusable components\n• Add proper error handling\n• Implement unit tests\n\n🚀 Ready to level up your code?",
    ]
    return responses[Math.floor(Math.random() * responses.length)]
  }

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion)
  }

  const quickActions = [
    { icon: Code, label: "Explain Code", action: "Explain the selected code", color: "from-blue-500 to-cyan-500" },
    {
      icon: Lightbulb,
      label: "Suggest Improvements",
      action: "Suggest improvements for this code",
      color: "from-yellow-500 to-orange-500",
    },
    {
      icon: TestTube,
      label: "Generate Tests",
      action: "Generate unit tests for this function",
      color: "from-purple-500 to-pink-500",
    },
    {
      icon: FileText,
      label: "Add Documentation",
      action: "Add JSDoc documentation to this code",
      color: "from-green-500 to-emerald-500",
    },
  ]

  if (isCollapsed) return null

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-white via-green-50/30 to-emerald-50/50 dark:from-gray-900 dark:via-gray-900 dark:to-green-950/20">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-green-200/50 dark:border-green-800/30 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
              <Bot className="w-5 h-5 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <Sparkles className="w-2.5 h-2.5 text-white" />
            </div>
          </div>
          <div>
            <h3 className="font-bold text-gray-900 dark:text-white flex items-center gap-2">
              AI Assistant
              <Zap className="w-4 h-4 text-green-500" />
            </h3>
            <p className="text-xs text-green-600 dark:text-green-400 font-medium">Powered by Advanced AI</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-xl hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors group"
        >
          <X className="w-5 h-5 text-gray-500 group-hover:text-red-500 transition-colors" />
        </button>
      </div>

      {/* Quick Actions */}
      <div className="p-6 border-b border-green-200/50 dark:border-green-800/30 bg-white/50 dark:bg-gray-900/50">
        <div className="flex items-center gap-2 mb-4">
          <Brain className="w-4 h-4 text-green-600 dark:text-green-400" />
          <h4 className="text-sm font-bold text-gray-900 dark:text-white">Quick Actions</h4>
        </div>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <button
                key={action.label}
                onClick={() => handleSuggestionClick(action.action)}
                className="group relative overflow-hidden flex items-center gap-3 p-3 rounded-xl border border-gray-200/50 dark:border-gray-700/50 hover:border-green-300 dark:hover:border-green-600 bg-white/80 dark:bg-gray-800/80 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200 hover:shadow-lg hover:shadow-green-500/10"
              >
                <div
                  className={`w-8 h-8 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center shadow-sm`}
                >
                  <Icon className="w-4 h-4 text-white" />
                </div>
                <div className="text-left">
                  <span className="text-xs font-semibold text-gray-900 dark:text-white block">{action.label}</span>
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              </button>
            )
          })}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.map((message) => (
          <div key={message.id} className={`flex gap-4 ${message.type === "user" ? "justify-end" : "justify-start"}`}>
            {message.type === "assistant" && (
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg shadow-green-500/25">
                <Bot className="w-5 h-5 text-white" />
              </div>
            )}

            <div className={`max-w-[85%] ${message.type === "user" ? "order-first" : ""}`}>
              <div
                className={`p-4 rounded-2xl shadow-sm ${
                  message.type === "user"
                    ? "bg-gradient-to-r from-green-600 to-emerald-600 text-white ml-auto shadow-green-500/20"
                    : "bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200/50 dark:border-gray-700/50"
                }`}
              >
                <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
              </div>

              {message.suggestions && (
                <div className="mt-3 space-y-2">
                  {message.suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="block w-full text-left px-4 py-2.5 text-xs font-medium text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20 rounded-xl hover:bg-green-100 dark:hover:bg-green-900/30 transition-all duration-200 border border-green-200/50 dark:border-green-800/30 hover:border-green-300 dark:hover:border-green-600 hover:shadow-sm"
                    >
                      💡 {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {message.type === "user" && (
              <div className="w-10 h-10 bg-gradient-to-r from-gray-600 to-gray-700 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                <User className="w-5 h-5 text-white" />
              </div>
            )}
          </div>
        ))}

        {isLoading && (
          <div className="flex gap-4">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
              <Bot className="w-5 h-5 text-white" />
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-4 border border-gray-200/50 dark:border-gray-700/50 shadow-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce"></div>
                <div
                  className="w-2 h-2 bg-green-500 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                ></div>
                <div
                  className="w-2 h-2 bg-green-500 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                ></div>
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">AI is thinking...</span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-6 border-t border-green-200/50 dark:border-green-800/30 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
        <div className="flex gap-3">
          <div className="flex-1 relative">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
              placeholder="Ask me anything about your code..."
              className="w-full px-4 py-3 pr-12 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 shadow-sm"
              disabled={isLoading}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Sparkles className="w-4 h-4 text-green-500" />
            </div>
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="p-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg shadow-green-500/25 hover:shadow-green-500/40 disabled:shadow-none"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  )
}
