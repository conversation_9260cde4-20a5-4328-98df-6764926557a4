"use client"

import type React from "react"

import { useState } from "react"
import { X, Upload, Globe, Lock, Link, ChevronRight, ChevronLeft, Check } from "lucide-react"
import { toast } from "./toast"
import Confetti from "./confetti"

interface NewProjectFormProps {
  onSubmit: (data: any) => void
  onCancel: () => void
}

export default function NewProjectForm({ onSubmit, onCancel }: NewProjectFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [showConfetti, setShowConfetti] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    icon: "📁",
    visibility: "private" as "public" | "private" | "link",
    techStack: [] as string[],
    files: [] as File[],
  })
  const [techInput, setTechInput] = useState("")

  const icons = ["📁", "🚀", "💻", "🎨", "📱", "🌐", "⚡", "🔧", "📊", "🎯", "💡", "🔥"]

  const steps = [
    { id: 1, title: "Basic Info", description: "Project name and description" },
    { id: 2, title: "Customization", description: "Icon and visibility settings" },
    { id: 3, title: "Tech Stack", description: "Technologies you'll use" },
    { id: 4, title: "Upload Files", description: "Add your project files" },
  ]

  const handleNext = () => {
    if (currentStep === 1 && !formData.name.trim()) {
      toast.error("Project name is required")
      return
    }
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    } else {
      handleSubmit()
    }
  }

  const handleSubmit = () => {
    setShowConfetti(true)
    setTimeout(() => {
      onSubmit(formData)
      setShowConfetti(false)
    }, 3000)
  }

  const addTech = () => {
    if (techInput.trim() && !formData.techStack.includes(techInput.trim())) {
      setFormData({
        ...formData,
        techStack: [...formData.techStack, techInput.trim()],
      })
      setTechInput("")
    }
  }

  const removeTech = (tech: string) => {
    setFormData({
      ...formData,
      techStack: formData.techStack.filter((t) => t !== tech),
    })
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setFormData({ ...formData, files })
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Project Name *</label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Enter project name..."
          className="w-full px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Brief description of your project..."
          rows={4}
          className="w-full px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all resize-none"
        />
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Project Icon</label>
        <div className="grid grid-cols-6 gap-3">
          {icons.map((icon) => (
            <button
              key={icon}
              type="button"
              onClick={() => setFormData({ ...formData, icon })}
              className={`w-16 h-16 rounded-xl border-2 flex items-center justify-center text-2xl transition-all ${
                formData.icon === icon
                  ? "border-green-500 bg-green-50 dark:bg-green-900/20 scale-110"
                  : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 hover:scale-105"
              }`}
            >
              {icon}
            </button>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Project Visibility</label>
        <div className="space-y-3">
          {[
            { value: "private", label: "Private", icon: Lock, desc: "Only invited members can access" },
            { value: "public", label: "Public", icon: Globe, desc: "Anyone on the network can access" },
            { value: "link", label: "Link Sharing", icon: Link, desc: "Anyone with the link can access" },
          ].map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => setFormData({ ...formData, visibility: option.value as any })}
              className={`w-full p-4 rounded-xl border-2 text-left transition-all ${
                formData.visibility === option.value
                  ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                  : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
              }`}
            >
              <div className="flex items-center gap-3 mb-2">
                <option.icon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <span className="font-medium text-gray-900 dark:text-white">{option.label}</span>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">{option.desc}</p>
            </button>
          ))}
        </div>
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tech Stack</label>
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={techInput}
            onChange={(e) => setTechInput(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTech())}
            placeholder="Add technology (e.g., React, Node.js)"
            className="flex-1 px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
          />
          <button
            type="button"
            onClick={addTech}
            className="px-6 py-3 rounded-lg bg-green-600 text-white hover:bg-green-700 transition-colors"
          >
            Add
          </button>
        </div>
        <div className="flex flex-wrap gap-2">
          {formData.techStack.map((tech) => (
            <span
              key={tech}
              className="px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-lg text-sm flex items-center gap-2"
            >
              {tech}
              <button type="button" onClick={() => removeTech(tech)} className="text-green-500 hover:text-red-500">
                <X className="w-3 h-3" />
              </button>
            </span>
          ))}
        </div>
        {formData.techStack.length === 0 && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
            Add technologies to help others understand your project stack
          </p>
        )}
      </div>

      {/* Popular Tech Stack Suggestions */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Popular Choices</label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {["React", "Vue.js", "Angular", "Node.js", "Python", "TypeScript", "JavaScript", "MongoDB", "PostgreSQL"].map(
            (tech) => (
              <button
                key={tech}
                type="button"
                onClick={() => {
                  if (!formData.techStack.includes(tech)) {
                    setFormData({ ...formData, techStack: [...formData.techStack, tech] })
                  }
                }}
                disabled={formData.techStack.includes(tech)}
                className="px-3 py-2 text-sm rounded-lg border border-gray-200 dark:border-gray-600 hover:border-green-500 dark:hover:border-green-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {tech}
              </button>
            ),
          )}
        </div>
      </div>
    </div>
  )

  const renderStep4 = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Project Files</label>
        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-12 text-center hover:border-green-500 dark:hover:border-green-400 transition-colors">
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
            Drag and drop files here, or click to browse
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">Supports all file types • Max 100MB per file</p>
          <input type="file" multiple onChange={handleFileUpload} className="hidden" id="file-upload" />
          <label
            htmlFor="file-upload"
            className="inline-flex items-center px-6 py-3 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors cursor-pointer"
          >
            Choose Files
          </label>
        </div>

        {formData.files.length > 0 && (
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Selected Files ({formData.files.length})
            </p>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {formData.files.map((file, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-sm text-gray-600 dark:text-gray-400">{file.name}</span>
                  <span className="text-xs text-gray-500">({(file.size / 1024).toFixed(1)} KB)</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
        <p className="text-sm text-blue-700 dark:text-blue-300">
          <strong>Tip:</strong> You can also create an empty project and add files later through the workspace.
        </p>
      </div>
    </div>
  )

  if (showConfetti) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-12 shadow-lg text-center">
        <Confetti />
        <div className="w-20 h-20 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mx-auto mb-6">
          <Check className="w-10 h-10 text-green-600 dark:text-green-400" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Project Created Successfully!</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Your project "{formData.name}" has been created and is ready to use.
        </p>
        <div className="text-4xl mb-4">{formData.icon}</div>
        <p className="text-sm text-gray-500 dark:text-gray-500">Redirecting to your projects...</p>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div>
          <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">Create New Project</h3>
          <p className="text-gray-600 dark:text-gray-400">
            Step {currentStep} of {steps.length}
          </p>
        </div>
        <button
          onClick={onCancel}
          className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>
      </div>

      {/* Progress Steps */}
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700/50">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                  currentStep >= step.id
                    ? "bg-green-600 text-white"
                    : "bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                }`}
              >
                {currentStep > step.id ? <Check className="w-4 h-4" /> : step.id}
              </div>
              <div className="ml-3">
                <p
                  className={`text-sm font-medium ${
                    currentStep >= step.id ? "text-green-600 dark:text-green-400" : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {step.title}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">{step.description}</p>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`flex-1 h-px mx-4 ${
                    currentStep > step.id ? "bg-green-600" : "bg-gray-200 dark:bg-gray-600"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Form Content */}
      <div className="p-8">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
        {currentStep === 4 && renderStep4()}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
          disabled={currentStep === 1}
          className="flex items-center gap-2 px-4 py-2 rounded-lg border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="w-4 h-4" />
          Previous
        </button>

        <div className="flex items-center gap-3">
          <button
            onClick={onCancel}
            className="px-6 py-2 rounded-lg border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleNext}
            className="flex items-center gap-2 px-6 py-2 rounded-lg bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
          >
            {currentStep === 4 ? "Create Project" : "Next"}
            {currentStep < 4 && <ChevronRight className="w-4 h-4" />}
          </button>
        </div>
      </div>
    </div>
  )
}
