"use client"

import {
  ArrowLeft,
  MoreVertical,
  Users,
  Settings,
  UserPlus,
  UserMinus,
  Shield,
  FileText,
  Folder,
  ChevronRight,
  Circle,
  MessageSquare,
  Send,
  Plus,
  Hash,
  GitBranch,
  Clock,
  Code,
  Globe,
  Lock,
  LinkIcon,
  Zap,
  TrendingUp,
} from "lucide-react"
import { useState } from "react"
import { toast } from "./toast"

interface Project {
  id: string
  name: string
  icon: string
  techStack: string[]
  fileCount: number
  visibility: "public" | "private" | "link"
  bannerColor: string
  collaborators: Array<{
    id: string
    name: string
    avatar: string
    role?: "admin" | "editor" | "viewer"
  }>
  lastEdited: string
  created: string
  description?: string
}

interface ProjectDetailsProps {
  project: Project
  onBack: () => void
  onUpdateBannerColor: (projectId: string, bannerColor: string) => void
}

interface FileNode {
  name: string
  type: "file" | "folder"
  children?: FileNode[]
  isOpen?: boolean
}

interface ChatGroup {
  id: string
  name: string
  members: number
  lastMessage: string
  timestamp: string
}

const FILE_TREE: FileNode[] = [
  {
    name: "src",
    type: "folder",
    isOpen: true,
    children: [
      {
        name: "components",
        type: "folder",
        isOpen: true,
        children: [
          { name: "Header.tsx", type: "file" },
          { name: "Sidebar.tsx", type: "file" },
          { name: "Button.tsx", type: "file" },
        ],
      },
      { name: "App.tsx", type: "file" },
      { name: "index.tsx", type: "file" },
    ],
  },
  { name: "package.json", type: "file" },
  { name: "README.md", type: "file" },
]

export default function ProjectDetails({ project, onBack, onUpdateBannerColor }: ProjectDetailsProps) {
  const [showMenu, setShowMenu] = useState(false)
  const [showTeamChat, setShowTeamChat] = useState(false)
  const [showCreateGroup, setShowCreateGroup] = useState(false)
  const [newGroupName, setNewGroupName] = useState("")
  const [chatGroups, setChatGroups] = useState<ChatGroup[]>([
    {
      id: "1",
      name: "General",
      members: 3,
      lastMessage: "Great work on the authentication module!",
      timestamp: "2 min ago",
    },
    {
      id: "2",
      name: "Frontend Team",
      members: 2,
      lastMessage: "UI components are looking good",
      timestamp: "1 hour ago",
    },
  ])
  const [selectedGroup, setSelectedGroup] = useState<ChatGroup | null>(chatGroups[0])
  const [messages, setMessages] = useState([
    {
      id: "1",
      user: "Sarah Kim",
      avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
      content: "Great work on the authentication module!",
      timestamp: "2:30 PM",
    },
    {
      id: "2",
      user: "Alex Chen",
      avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
      content: "Thanks! The login flow is much smoother now.",
      timestamp: "2:32 PM",
    },
  ])
  const [newMessage, setNewMessage] = useState("")

  const bannerColors = [
    "from-blue-500 to-purple-600",
    "from-green-500 to-emerald-600",
    "from-red-500 to-pink-600",
    "from-yellow-500 to-orange-600",
    "from-purple-500 to-indigo-600",
    "from-teal-500 to-cyan-600",
    "from-gray-500 to-gray-600",
  ]

  const handleCreateGroup = () => {
    if (!newGroupName.trim()) {
      toast.error("Group name is required")
      return
    }

    const newGroup: ChatGroup = {
      id: Date.now().toString(),
      name: newGroupName,
      members: 1,
      lastMessage: "Group created",
      timestamp: "now",
    }

    setChatGroups([...chatGroups, newGroup])
    setNewGroupName("")
    setShowCreateGroup(false)
    toast.success(`Group "${newGroupName}" created successfully`)
  }

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    const message = {
      id: Date.now().toString(),
      user: "You",
      avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
      content: newMessage,
      timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    }

    setMessages([...messages, message])
    setNewMessage("")
  }

  const renderFileTree = (nodes: FileNode[], depth = 0) => {
    return nodes.map((node, index) => (
      <div key={`${node.name}-${index}`} className="select-none">
        <div
          className="flex items-center gap-2 py-2 px-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 cursor-pointer transition-colors"
          style={{ paddingLeft: `${depth * 16 + 12}px` }}
        >
          {node.type === "folder" ? (
            <>
              <ChevronRight
                className={`w-4 h-4 text-gray-400 transition-transform ${node.isOpen ? "rotate-90" : ""}`}
              />
              <Folder className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </>
          ) : (
            <>
              <div className="w-4" />
              <Circle className="w-3 h-3 text-gray-400" />
            </>
          )}
          <span className="text-sm text-gray-700 dark:text-gray-300">{node.name}</span>
        </div>
        {node.type === "folder" && node.isOpen && node.children && (
          <div>{renderFileTree(node.children, depth + 1)}</div>
        )}
      </div>
    ))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        </div>

        {/* Header */}
        <div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50">
          <div className="max-w-7xl mx-auto px-8 py-6">
            <div className="flex items-center justify-between">
              <button
                onClick={onBack}
                className="flex items-center gap-2 px-4 py-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-600 dark:text-gray-300"
              >
                <ArrowLeft className="w-5 h-5" />
                <span className="font-medium">Back to Projects</span>
              </button>

              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowTeamChat(true)}
                  className="flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg"
                >
                  <MessageSquare className="w-4 h-4" />
                  Team Chat
                </button>

                <div className="relative">
                  <button
                    onClick={() => setShowMenu(!showMenu)}
                    className="p-3 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <MoreVertical className="w-5 h-5 text-gray-600 dark:text-gray-300" />
                  </button>
                  {showMenu && (
                    <div className="absolute right-0 top-14 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl z-10 min-w-[200px] overflow-hidden">
                      <button
                        onClick={() => {
                          toast.info("Project settings opened")
                          setShowMenu(false)
                        }}
                        className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <Settings className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-700 dark:text-gray-300">Project Settings</span>
                      </button>
                      <button
                        onClick={() => {
                          toast.info("Add people dialog opened")
                          setShowMenu(false)
                        }}
                        className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <UserPlus className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-700 dark:text-gray-300">Add People</span>
                      </button>
                      <button
                        onClick={() => {
                          toast.info("Manage permissions opened")
                          setShowMenu(false)
                        }}
                        className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <Shield className="w-4 h-4 text-gray-500" />
                        <span className="text-gray-700 dark:text-gray-300">Manage Permissions</span>
                      </button>
                      <div className="border-t border-gray-200 dark:border-gray-600">
                        <button
                          onClick={() => {
                            toast.info("Remove people dialog opened")
                            setShowMenu(false)
                          }}
                          className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 transition-colors"
                        >
                          <UserMinus className="w-4 h-4" />
                          <span>Remove People</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Project Hero */}
        <div className="relative max-w-7xl mx-auto px-8 py-16">
          <div className={`rounded-3xl bg-gradient-to-r ${project.bannerColor} p-12 text-white shadow-2xl`}>
            <div className="flex items-start gap-8">
              <div className="text-8xl drop-shadow-lg">{project.icon}</div>
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-4">
                  <h1 className="text-5xl font-bold">{project.name}</h1>
                  <div className="flex items-center gap-2">
                    {project.visibility === "public" && <Globe className="w-6 h-6 opacity-80" />}
                    {project.visibility === "private" && <Lock className="w-6 h-6 opacity-80" />}
                    {project.visibility === "link" && <LinkIcon className="w-6 h-6 opacity-80" />}
                  </div>
                </div>
                <p className="text-xl opacity-90 mb-8 max-w-2xl leading-relaxed">{project.description}</p>

                <div className="flex flex-wrap items-center gap-6">
                  <div className="flex flex-wrap gap-3">
                    {project.techStack.map((tech) => (
                      <span key={tech} className="px-4 py-2 rounded-xl bg-white/20 backdrop-blur-sm font-medium">
                        {tech}
                      </span>
                    ))}
                  </div>
                  <div className="flex items-center gap-6 text-white/80">
                    <div className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      <span>{project.fileCount} files</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      <span>Created {project.created}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Banner Color Selector */}
          <div className="mt-8 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
              Customize Banner Color
            </label>
            <div className="flex gap-3">
              {bannerColors.map((color) => (
                <button
                  key={color}
                  onClick={() => onUpdateBannerColor(project.id, color)}
                  className={`w-12 h-12 rounded-xl bg-gradient-to-r ${color} ${
                    project.bannerColor === color ? "ring-4 ring-gray-400 ring-offset-2 dark:ring-offset-gray-800" : ""
                  } hover:scale-110 transition-transform shadow-lg`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 rounded-xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                <Code className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">127</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Files</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <GitBranch className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">23</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Commits</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 rounded-xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                <Users className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{project.collaborators.length}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Collaborators</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 rounded-xl bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center">
                <TrendingUp className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">98%</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Progress</p>
              </div>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 border border-gray-200 dark:border-gray-700 shadow-sm mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Users className="w-6 h-6" />
              Team Members
            </h3>
            <button className="flex items-center gap-2 px-4 py-2 rounded-xl bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors">
              <UserPlus className="w-4 h-4" />
              Invite
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {project.collaborators.map((collaborator) => (
              <div
                key={collaborator.id}
                className="flex items-center gap-3 p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50"
              >
                <img
                  src={collaborator.avatar || "/placeholder.svg"}
                  alt={collaborator.name}
                  className="w-12 h-12 rounded-full"
                />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-white">{collaborator.name}</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">{collaborator.role || "editor"}</p>
                </div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* File Structure */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Project Structure
              </h3>
            </div>
            <div className="p-4 max-h-96 overflow-y-auto">{renderFileTree(FILE_TREE)}</div>
          </div>

          {/* Shared Files */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                <Globe className="w-5 h-5 text-blue-600" />
                Shared Files
              </h3>
            </div>

            <div className="p-6 space-y-4 max-h-96 overflow-y-auto">
              {/* Upload Area */}
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-8 text-center hover:border-blue-500 dark:hover:border-blue-400 transition-colors cursor-pointer group">
                <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                  <Plus className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <p className="text-gray-600 dark:text-gray-400 mb-2">Drop files here or click to upload</p>
                <p className="text-sm text-gray-500 dark:text-gray-500">Share files with your team</p>
              </div>

              {/* Shared Files List */}
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                  <div className="w-10 h-10 rounded-lg bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                    <FileText className="w-5 h-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 dark:text-white truncate">Project Requirements.pdf</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Uploaded by Sarah Kim • 2 hours ago</p>
                  </div>
                  <div className="flex -space-x-2">
                    <img
                      src="https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png"
                      alt="Sarah"
                      className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800"
                    />
                    <img
                      src="https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png"
                      alt="Alex"
                      className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800"
                    />
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                  <div className="w-10 h-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                    <FileText className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 dark:text-white truncate">Design Mockups.zip</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Uploaded by Mike Johnson • 1 day ago</p>
                  </div>
                  <div className="flex -space-x-2">
                    <img
                      src="https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png"
                      alt="Mike"
                      className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800"
                    />
                    <img
                      src="https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png"
                      alt="Sarah"
                      className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800"
                    />
                    <img
                      src="https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png"
                      alt="Alex"
                      className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800"
                    />
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                  <div className="w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                    <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 dark:text-white truncate">API Documentation.md</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Uploaded by Alex Chen • 3 days ago</p>
                  </div>
                  <div className="flex -space-x-2">
                    <img
                      src="https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png"
                      alt="Alex"
                      className="w-6 h-6 rounded-full border-2 border-white dark:border-gray-800"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 dark:border-gray-700">
              <button className="w-full py-3 px-4 rounded-xl bg-blue-600 text-white hover:bg-blue-700 transition-colors font-medium">
                View All Files
              </button>
            </div>
          </div>

          {/* AI Assistant */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                <Zap className="w-5 h-5 text-purple-600" />
                AI Project Assistant
              </h3>
            </div>

            <div className="p-6 space-y-4 max-h-96 overflow-y-auto">
              <div className="flex justify-start">
                <div className="max-w-[80%] p-4 rounded-2xl bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white">
                  <p className="text-sm">
                    Hello! I'm your AI assistant for the {project.name} project. I can help you understand the codebase,
                    generate documentation, or answer questions about the project structure.
                  </p>
                  <p className="text-xs mt-2 text-gray-500 dark:text-gray-400">2:30 PM</p>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder="Ask about this project..."
                  className="flex-1 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                />
                <button className="px-6 py-3 rounded-xl bg-purple-600 text-white hover:bg-purple-700 transition-colors">
                  <Send className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Team Chat Modal */}
      {showTeamChat && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-5xl h-[85vh] bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 flex overflow-hidden">
            {/* Chat Groups Sidebar */}
            <div className="w-80 border-r border-gray-200 dark:border-gray-700 p-6 bg-gray-50 dark:bg-gray-700/50">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Team Chat</h3>
                <button
                  onClick={() => setShowCreateGroup(true)}
                  className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  <Plus className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                </button>
              </div>

              <div className="space-y-2">
                {chatGroups.map((group) => (
                  <button
                    key={group.id}
                    onClick={() => setSelectedGroup(group)}
                    className={`w-full p-4 rounded-xl text-left transition-colors ${
                      selectedGroup?.id === group.id
                        ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
                        : "hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-2">
                      <Hash className="w-4 h-4" />
                      <span className="font-medium">{group.name}</span>
                      <span className="text-xs text-gray-500 ml-auto">{group.members}</span>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{group.lastMessage}</p>
                    <p className="text-xs text-gray-400 mt-1">{group.timestamp}</p>
                  </button>
                ))}
              </div>

              {/* Create Group Form */}
              {showCreateGroup && (
                <div className="mt-6 p-4 border border-gray-200 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Create New Group</h4>
                  <input
                    type="text"
                    value={newGroupName}
                    onChange={(e) => setNewGroupName(e.target.value)}
                    placeholder="Group name..."
                    className="w-full px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 mb-3"
                  />
                  <div className="flex gap-2">
                    <button
                      onClick={handleCreateGroup}
                      className="flex-1 py-2 px-3 rounded-lg bg-green-600 text-white hover:bg-green-700 transition-colors"
                    >
                      Create
                    </button>
                    <button
                      onClick={() => setShowCreateGroup(false)}
                      className="flex-1 py-2 px-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Chat Area */}
            <div className="flex-1 flex flex-col">
              {/* Chat Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                <div className="flex items-center gap-2">
                  <Hash className="w-5 h-5 text-gray-500" />
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {selectedGroup?.name || "Select a group"}
                  </h4>
                  {selectedGroup && (
                    <span className="text-sm text-gray-500 dark:text-gray-400">{selectedGroup.members} members</span>
                  )}
                </div>
                <button
                  onClick={() => setShowTeamChat(false)}
                  className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                >
                  ×
                </button>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50 dark:bg-gray-900/50">
                {messages.map((message) => (
                  <div key={message.id} className="flex gap-3">
                    <img
                      src={message.avatar || "/placeholder.svg"}
                      alt={message.user}
                      className="w-10 h-10 rounded-full"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-gray-900 dark:text-white">{message.user}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">{message.timestamp}</span>
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-2xl p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                        <p className="text-gray-700 dark:text-gray-300">{message.content}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                <div className="flex gap-3">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                    placeholder={`Message #${selectedGroup?.name || "group"}...`}
                    className="flex-1 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                  />
                  <button
                    onClick={handleSendMessage}
                    className="px-6 py-3 rounded-xl bg-green-600 text-white hover:bg-green-700 transition-colors"
                  >
                    <Send className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
