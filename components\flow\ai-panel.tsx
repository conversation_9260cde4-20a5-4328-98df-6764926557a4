"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, FileText, BookOpen, <PERSON>rkles, Send, Copy, Download } from "lucide-react"
import { useState } from "react"

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: string
}

interface AIPanelProps {
  onClose: () => void
}

export default function AIPanel({ onClose }: AIPanelProps) {
  const [activeTab, setActiveTab] = useState<"chat" | "explain" | "docs" | "summary">("chat")
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "ai",
      content:
        "Hello! I'm your local AI assistant. I can help explain your code, generate documentation, or answer questions about your project. What would you like to know?",
      timestamp: "2:30 PM",
    },
  ])
  const [inputValue, setInputValue] = useState("")

  const tabs = [
    { id: "chat", label: "Chat", icon: MessageSquare },
    { id: "explain", label: "Explain File", icon: FileText },
    { id: "docs", label: "Generate Docs", icon: Book<PERSON>pen },
    { id: "summary", label: "Summarize", icon: Sparkles },
  ]

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    }

    setMessages([...messages, newMessage])
    setInputValue("")

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: "I understand your question. Let me analyze your code and provide a detailed explanation...",
        timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      }
      setMessages((prev) => [...prev, aiResponse])
    }, 1000)
  }

  const renderChatTab = () => (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}>
            <div
              className={`max-w-[80%] p-4 rounded-2xl ${
                message.type === "user"
                  ? "bg-emerald-500 text-white"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
              }`}
            >
              <p className="text-sm">{message.content}</p>
              <p
                className={`text-xs mt-2 ${
                  message.type === "user" ? "text-emerald-100" : "text-gray-500 dark:text-gray-400"
                }`}
              >
                {message.timestamp}
              </p>
            </div>
          </div>
        ))}
      </div>

      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
            placeholder="Ask about your code..."
            className="flex-1 px-4 py-3 rounded-2xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500"
          />
          <button
            onClick={handleSendMessage}
            className="p-3 rounded-2xl bg-emerald-500 text-white hover:bg-emerald-600 transition-colors"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  )

  const renderExplainTab = () => (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 rounded-3xl bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mx-auto mb-4">
          <FileText className="w-8 h-8 text-emerald-600 dark:text-emerald-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Explain Current File</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">Get AI-powered explanations of your code</p>
      </div>

      <div className="space-y-4">
        <button className="w-full p-4 rounded-2xl bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 text-left hover:bg-emerald-100 dark:hover:bg-emerald-900/30 transition-colors">
          <h4 className="font-medium text-emerald-900 dark:text-emerald-100 mb-1">Explain App.tsx</h4>
          <p className="text-sm text-emerald-700 dark:text-emerald-300">Analyze the main application component</p>
        </button>

        <button className="w-full p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
          <h4 className="font-medium text-gray-900 dark:text-white mb-1">Explain Function</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">Explain selected code block</p>
        </button>
      </div>
    </div>
  )

  const renderDocsTab = () => (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 rounded-3xl bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mx-auto mb-4">
          <BookOpen className="w-8 h-8 text-blue-600 dark:text-blue-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Generate Documentation</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">Auto-generate docs for your project</p>
      </div>

      <div className="space-y-4">
        <button className="w-full p-4 rounded-2xl bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-left hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">README.md</h4>
          <p className="text-sm text-blue-700 dark:text-blue-300">Generate project overview and setup guide</p>
        </button>

        <button className="w-full p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
          <h4 className="font-medium text-gray-900 dark:text-white mb-1">API Documentation</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">Document all API endpoints and functions</p>
        </button>
      </div>
    </div>
  )

  const renderSummaryTab = () => (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 rounded-3xl bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center mx-auto mb-4">
          <Sparkles className="w-8 h-8 text-purple-600 dark:text-purple-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Project Summary</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">Get insights about your entire project</p>
      </div>

      <div className="p-4 rounded-2xl bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
        <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-3">Project Analysis</h4>
        <div className="space-y-2 text-sm text-purple-700 dark:text-purple-300">
          <p>• 23 React components</p>
          <p>• 8 custom hooks</p>
          <p>• TypeScript coverage: 95%</p>
          <p>• 156 lines of code</p>
        </div>
        <div className="flex gap-2 mt-4">
          <button className="flex items-center gap-2 px-3 py-2 rounded-xl bg-white dark:bg-gray-700 text-purple-700 dark:text-purple-300 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
            <Copy className="w-4 h-4" />
            Copy
          </button>
          <button className="flex items-center gap-2 px-3 py-2 rounded-xl bg-white dark:bg-gray-700 text-purple-700 dark:text-purple-300 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl h-[80vh] bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">AI Assistant</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Local AI powered by Ollama</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors ${
                  activeTab === tab.id
                    ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                    : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === "chat" && renderChatTab()}
          {activeTab === "explain" && renderExplainTab()}
          {activeTab === "docs" && renderDocsTab()}
          {activeTab === "summary" && renderSummaryTab()}
        </div>
      </div>
    </div>
  )
}
