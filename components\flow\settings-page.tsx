"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, Shield, FolderSync, Wifi, Monitor, Bell, Database, GitCommit } from "lucide-react"
import { useState } from "react"

interface Project {
  id: string
  name: string
  icon: string
  bannerColor: string
}

interface SettingsPageProps {
  projects: Project[]
  onUpdateBannerColor: (projectId: string, bannerColor: string) => void
}

export default function SettingsPage({ projects, onUpdateBannerColor }: SettingsPageProps) {
  const [activeSection, setActiveSection] = useState("general")
  const [commitLifetime, setCommitLifetime] = useState("1week")

  const sections = [
    { id: "general", label: "General", icon: Monitor },
    { id: "appearance", label: "Appearance", icon: Palette },
    { id: "projects", label: "Project Colors", icon: Palette },
    { id: "commits", label: "Version Control", icon: GitCommit },
    { id: "ai", label: "AI Engine", icon: Cpu },
    { id: "network", label: "Network", icon: Wifi },
    { id: "permissions", label: "Permissions", icon: Shield },
    { id: "sync", label: "File Sync", icon: FolderSync },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "storage", label: "Storage", icon: Database },
  ]

  const bannerColors = [
    { name: "Blue Purple", gradient: "from-blue-500 to-purple-600" },
    { name: "Green Emerald", gradient: "from-green-500 to-emerald-600" },
    { name: "Red Pink", gradient: "from-red-500 to-pink-600" },
    { name: "Yellow Orange", gradient: "from-yellow-500 to-orange-600" },
    { name: "Purple Indigo", gradient: "from-purple-500 to-indigo-600" },
  ];

  return (
    <div className="flex h-full">
      {/* Settings Sidebar */}
      <div className="w-64 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="p-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Settings</h2>
          <nav className="space-y-1">
            {sections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeSection === section.id
                      ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700"
                  }`}
                >
                  <Icon className="mr-3 h-4 w-4" />
                  {section.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Settings Content */}
      <div className="flex-1 p-6">
        {activeSection === "general" && (
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">General Settings</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Application Theme
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white">
                  <option>System</option>
                  <option>Light</option>
                  <option>Dark</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {activeSection === "projects" && (
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Project Colors</h3>
            <div className="space-y-4">
              {projects.map((project) => (
                <div key={project.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg dark:border-gray-700">
                  <div className="flex items-center">
                    <div className="text-2xl mr-3">{project.icon}</div>
                    <span className="font-medium text-gray-900 dark:text-white">{project.name}</span>
                  </div>
                  <div className="flex space-x-2">
                    {bannerColors.map((color) => (
                      <button
                        key={color.name}
                        onClick={() => onUpdateBannerColor(project.id, color.gradient)}
                        className={`w-8 h-8 rounded-full bg-gradient-to-r ${color.gradient} border-2 ${
                          project.bannerColor === color.gradient ? "border-gray-900 dark:border-white" : "border-transparent"
                        }`}
                        title={color.name}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeSection === "commits" && (
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Version Control</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Commit Lifetime
                </label>
                <select
                  value={commitLifetime}
                  onChange={(e) => setCommitLifetime(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="1day">1 Day</option>
                  <option value="1week">1 Week</option>
                  <option value="1month">1 Month</option>
                  <option value="3months">3 Months</option>
                  <option value="forever">Forever</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Add other sections as needed */}
        {activeSection !== "general" && activeSection !== "projects" && activeSection !== "commits" && (
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              {sections.find(s => s.id === activeSection)?.label} Settings
            </h3>
            <p className="text-gray-600 dark:text-gray-400">Settings for this section are coming soon.</p>
          </div>
        )}
      </div>
    </div>
  );
}
