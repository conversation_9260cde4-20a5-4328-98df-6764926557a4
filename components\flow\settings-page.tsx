"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, Shield, FolderSync, Wifi, Monitor, Bell, Database, GitCommit } from "lucide-react"
import { useState } from "react"

interface Project {
  id: string
  name: string
  icon: string
  bannerColor: string
}

interface SettingsPageProps {
  projects: Project[]
  onUpdateBannerColor: (projectId: string, bannerColor: string) => void
}

export default function SettingsPage({ projects, onUpdateBannerColor }: SettingsPageProps) {
  const [activeSection, setActiveSection] = useState("general")
  const [commitLifetime, setCommitLifetime] = useState("1week")

  const sections = [
    { id: "general", label: "General", icon: Monitor },
    { id: "appearance", label: "Appearance", icon: Palette },
    { id: "projects", label: "Project Colors", icon: Palette },
    { id: "commits", label: "Version Control", icon: GitCommit },
    { id: "ai", label: "AI Engine", icon: Cpu },
    { id: "network", label: "Network", icon: Wifi },
    { id: "permissions", label: "Permissions", icon: Shield },
    { id: "sync", label: "File Sync", icon: FolderSync },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "storage", label: "Storage", icon: Database },
  ]

  const bannerColors = [
    { name: "Blue Purple", gradient: "from-blue-500 to-purple-600" },
    { name: "Green Emerald", gradient: "from-green-500 to-emerald-600" },
    { name: "Red Pink", gradient: "from-red-500 to-pink-600" },
    { name: "Yellow Orange", gradient: "from-yellow-500 to-orange-600" },
    { name: "Purple Indigo", gradient: "from-purple-500 to-indigo-600" },
    \
