import type React from "react"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { ToastContainer } from "@/components/flow/toast"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Flow - Local Collaboration",
  description: "Local-first developer collaboration tool",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          {children}
          <ToastContainer />
        </ThemeProvider>
      </body>
    </html>
  )
}
