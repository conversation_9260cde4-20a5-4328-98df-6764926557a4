"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, FileText, Send, HighlighterIcon as Highlight, Zap, Brain, Code, BookOpen } from "lucide-react"

interface Project {
  id: string
  name: string
  icon: string
}

interface AIAssistantPageProps {
  projects: Project[]
}

interface Message {
  id: string
  type: "user" | "ai"
  content: string
  timestamp: string
  highlightedText?: string
}

export default function AIAssistantPage({ projects }: AIAssistantPageProps) {
  const [selectedProject, setSelectedProject] = useState<Project | null>(projects[0] || null)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      type: "ai",
      content:
        "Hello! I'm your AI assistant. You can highlight any text in your project files and ask me questions about it. I can help explain code, suggest improvements, or generate documentation.",
      timestamp: "2:30 PM",
    },
  ])
  const [inputValue, setInputValue] = useState("")
  const [highlightedText, setHighlightedText] = useState("")
  const [codeContent, setCodeContent] = useState(`import React, { useState, useEffect } from 'react';
import { User, Project } from '../types';

interface DashboardProps {
  user: User;
  projects: Project[];
}

const Dashboard: React.FC<DashboardProps> = ({ user, projects }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch user data and projects
    const fetchData = async () => {
      try {
        setLoading(true);
        // API calls here
        await new Promise(resolve => setTimeout(resolve, 1000));
        setLoading(false);
      } catch (err) {
        setError('Failed to load data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="dashboard">
      <h1>Welcome, {user.name}!</h1>
      <div className="projects-grid">
        {projects.map(project => (
          <div key={project.id} className="project-card">
            <h3>{project.name}</h3>
            <p>{project.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Dashboard;`)

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
      timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      highlightedText: highlightedText || undefined,
    }

    setMessages([...messages, newMessage])
    setInputValue("")

    // Simulate AI response
    setTimeout(() => {
      let aiResponse = "I understand your question. Let me analyze the code and provide insights..."

      if (highlightedText) {
        if (highlightedText.includes("useEffect")) {
          aiResponse =
            "This useEffect hook is used for side effects in React components. It runs after the component mounts and handles data fetching with proper loading and error states. The empty dependency array [] means it only runs once when the component mounts."
        } else if (highlightedText.includes("useState")) {
          aiResponse =
            "These useState hooks manage component state. 'loading' tracks the loading state, 'error' stores any error messages. This is a common pattern for handling async operations in React."
        } else if (highlightedText.includes("interface")) {
          aiResponse =
            "This TypeScript interface defines the props structure for the Dashboard component. It ensures type safety by specifying that the component expects a 'user' object and an array of 'projects'."
        } else {
          aiResponse = `I can see you've highlighted: "${highlightedText}". This appears to be part of a React component. Would you like me to explain how this code works or suggest any improvements?`
        }
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: aiResponse,
        timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      }
      setMessages((prev) => [...prev, aiMessage])
    }, 1000)

    setHighlightedText("")
  }

  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setHighlightedText(selection.toString().trim())
    }
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 mb-6">
          <Sparkles className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          <span className="text-purple-700 dark:text-purple-300 font-medium">AI-Powered Code Assistant</span>
        </div>
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Intelligent Code Analysis & Assistance
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Highlight code, ask questions, and get AI-powered insights to understand your codebase better
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-2xl p-6 border border-purple-200 dark:border-purple-800">
          <div className="w-12 h-12 rounded-xl bg-purple-600 flex items-center justify-center mb-4">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Code Explanation</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Highlight any code snippet to get detailed explanations
          </p>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-800">
          <div className="w-12 h-12 rounded-xl bg-blue-600 flex items-center justify-center mb-4">
            <Code className="w-6 h-6 text-white" />
          </div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Code Review</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Get suggestions for improvements and best practices
          </p>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-2xl p-6 border border-green-200 dark:border-green-800">
          <div className="w-12 h-12 rounded-xl bg-green-600 flex items-center justify-center mb-4">
            <BookOpen className="w-6 h-6 text-white" />
          </div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Generate Docs</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">Auto-generate documentation for your functions</p>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-2xl p-6 border border-yellow-200 dark:border-yellow-800">
          <div className="w-12 h-12 rounded-xl bg-yellow-600 flex items-center justify-center mb-4">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Quick Fixes</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">Get instant suggestions for common issues</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 h-[calc(100vh-400px)]">
        {/* Code Editor */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 flex flex-col shadow-lg">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-t-2xl">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Code Editor
              </h3>
              <select
                value={selectedProject?.id || ""}
                onChange={(e) => {
                  const project = projects.find((p) => p.id === e.target.value)
                  setSelectedProject(project || null)
                }}
                className="px-4 py-2 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium"
              >
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.icon} {project.name}
                  </option>
                ))}
              </select>
            </div>
            {highlightedText && (
              <div className="mt-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
                <div className="flex items-center gap-2 mb-2">
                  <Highlight className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Selected Text:</span>
                </div>
                <code className="text-sm text-yellow-700 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 px-3 py-2 rounded-lg block">
                  {highlightedText}
                </code>
              </div>
            )}
          </div>

          <div className="flex-1 p-6 overflow-auto bg-gray-900 text-gray-100 font-mono">
            <pre className="text-sm leading-relaxed select-text whitespace-pre-wrap" onMouseUp={handleTextSelection}>
              <code>{codeContent}</code>
            </pre>
          </div>
        </div>

        {/* AI Chat */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 flex flex-col shadow-lg">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-t-2xl">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-purple-600" />
              AI Assistant
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Highlight text in the code editor and ask questions
            </p>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50 dark:bg-gray-900/50">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}>
                <div className="max-w-[85%]">
                  {message.highlightedText && (
                    <div className="mb-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
                      <p className="text-xs text-yellow-600 dark:text-yellow-400 mb-1 font-medium">Highlighted:</p>
                      <code className="text-xs text-yellow-700 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded">
                        {message.highlightedText}
                      </code>
                    </div>
                  )}
                  <div
                    className={`p-4 rounded-2xl shadow-sm ${
                      message.type === "user"
                        ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white"
                        : "bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-700"
                    }`}
                  >
                    <p className="text-sm leading-relaxed">{message.content}</p>
                    <p
                      className={`text-xs mt-2 ${
                        message.type === "user" ? "text-green-100" : "text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      {message.timestamp}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Input */}
          <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <div className="flex gap-3">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                placeholder={highlightedText ? "Ask about the highlighted text..." : "Ask about the code..."}
                className="flex-1 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
              />
              <button
                onClick={handleSendMessage}
                className="px-6 py-3 rounded-xl bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 transition-all shadow-lg"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
