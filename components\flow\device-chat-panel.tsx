"use client"

import { X, Send, Phone, Video } from "lucide-react"
import { useState } from "react"

interface DeviceChatPanelProps {
  device: {
    id: string
    name: string
    user: string
    avatar: string
    status: "online" | "away" | "editing"
  }
  onClose: () => void
}

interface Message {
  id: string
  sender: "me" | "them"
  content: string
  timestamp: string
}

export default function DeviceChatPanel({ device, onClose }: DeviceChatPanelProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      sender: "them",
      content: "Hey! I'm working on the authentication module. How's your progress on the UI components?",
      timestamp: "2:30 PM",
    },
    {
      id: "2",
      sender: "me",
      content: "Going well! I've finished the login and signup forms. Want to review them together?",
      timestamp: "2:32 PM",
    },
    {
      id: "3",
      sender: "them",
      content: "I'll take a look at your branch. The forms look great from what I can see.",
      timestamp: "2:35 PM",
    },
  ])
  const [newMessage, setNewMessage] = useState("")

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    const message: Message = {
      id: Date.now().toString(),
      sender: "me",
      content: newMessage,
      timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    }

    setMessages([...messages, message])
    setNewMessage("")
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500"
      case "editing":
        return "bg-green-600"
      case "away":
        return "bg-yellow-500"
      default:
        return "bg-gray-400"
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl h-[80vh] bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-4">
            <div className="relative">
              <img src={device.avatar || "/placeholder.svg"} alt={device.user} className="w-12 h-12 rounded-full" />
              <div
                className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white dark:border-gray-800 ${getStatusColor(device.status)}`}
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{device.user}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {device.name} • <span className="capitalize">{device.status}</span>
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <Phone className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
            <button className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <Video className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
            <button
              onClick={onClose}
              className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => (
            <div key={message.id} className={`flex ${message.sender === "me" ? "justify-end" : "justify-start"}`}>
              <div className="flex items-start gap-3 max-w-[70%]">
                {message.sender === "them" && (
                  <img src={device.avatar || "/placeholder.svg"} alt={device.user} className="w-8 h-8 rounded-full" />
                )}
                <div>
                  <div
                    className={`p-4 rounded-2xl ${
                      message.sender === "me"
                        ? "bg-green-600 text-white"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white"
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                  </div>
                  <p
                    className={`text-xs mt-1 ${
                      message.sender === "me" ? "text-right" : "text-left"
                    } text-gray-500 dark:text-gray-400`}
                  >
                    {message.timestamp}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Message Input */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex gap-3">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
              placeholder={`Message ${device.user}...`}
              className="flex-1 px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
            />
            <button
              onClick={handleSendMessage}
              className="px-6 py-3 rounded-xl bg-green-600 text-white hover:bg-green-700 transition-colors"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
