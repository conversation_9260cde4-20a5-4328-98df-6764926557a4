"use client"

import { X, <PERSON>lette, <PERSON>pu, Shield, FolderSync, Monitor, Wifi } from "lucide-react"
import { useState } from "react"
import { ThemeToggle } from "../theme-toggle"

interface SettingsModalProps {
  onClose: () => void
}

export default function SettingsModal({ onClose }: SettingsModalProps) {
  const [activeSection, setActiveSection] = useState("appearance")

  const sections = [
    { id: "appearance", label: "Appearance", icon: Palette },
    { id: "ai", label: "AI Engine", icon: Cpu },
    { id: "permissions", label: "Permissions", icon: Shield },
    { id: "sync", label: "File Sync", icon: FolderSync },
    { id: "network", label: "Network", icon: Wifi },
  ]

  const renderAppearanceSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Theme</h3>
        <div className="flex items-center justify-between p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50">
          <div>
            <p className="font-medium text-gray-900 dark:text-white">Dark Mode</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">Toggle between light and dark themes</p>
          </div>
          <ThemeToggle />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Accent Color</h3>
        <div className="grid grid-cols-6 gap-3">
          {[
            { name: "Emerald", color: "bg-emerald-500", active: true },
            { name: "Blue", color: "bg-blue-500", active: false },
            { name: "Purple", color: "bg-purple-500", active: false },
            { name: "Pink", color: "bg-pink-500", active: false },
            { name: "Orange", color: "bg-orange-500", active: false },
            { name: "Red", color: "bg-red-500", active: false },
          ].map((color) => (
            <button
              key={color.name}
              className={`w-12 h-12 rounded-2xl ${color.color} ${
                color.active ? "ring-4 ring-gray-300 dark:ring-gray-600" : ""
              } hover:scale-110 transition-transform`}
            />
          ))}
        </div>
      </div>
    </div>
  )

  const renderAISection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">AI Provider</h3>
        <div className="space-y-3">
          {[
            { name: "Ollama", description: "Local AI models", active: true },
            { name: "llama.cpp", description: "Lightweight inference", active: false },
            { name: "Remote API", description: "Cloud-based AI", active: false },
          ].map((provider) => (
            <button
              key={provider.name}
              className={`w-full p-4 rounded-2xl border text-left transition-all ${
                provider.active
                  ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20"
                  : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
              }`}
            >
              <div className="font-medium text-gray-900 dark:text-white">{provider.name}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{provider.description}</div>
            </button>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Model Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50">
            <div>
              <p className="font-medium text-gray-900 dark:text-white">Model</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">llama3.2:3b</p>
            </div>
            <button className="px-4 py-2 rounded-xl bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 hover:bg-emerald-200 dark:hover:bg-emerald-900/50 transition-colors">
              Change
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  const renderPermissionsSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Access Control</h3>
        <div className="space-y-3">
          {[
            { name: "Allow file editing", description: "Let collaborators edit files", enabled: true },
            { name: "Allow port sharing", description: "Share development servers", enabled: true },
            { name: "Allow AI access", description: "Use AI assistant features", enabled: false },
          ].map((permission) => (
            <div
              key={permission.name}
              className="flex items-center justify-between p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50"
            >
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{permission.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{permission.description}</p>
              </div>
              <button
                className={`w-12 h-6 rounded-full transition-colors ${
                  permission.enabled ? "bg-emerald-500" : "bg-gray-300 dark:bg-gray-600"
                }`}
              >
                <div
                  className={`w-5 h-5 rounded-full bg-white shadow-sm transition-transform ${
                    permission.enabled ? "translate-x-6" : "translate-x-0.5"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderSyncSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sync Settings</h3>
        <div className="space-y-3">
          {[
            { name: "Auto-sync files", description: "Automatically sync file changes", enabled: true },
            { name: "Sync on save", description: "Sync when files are saved", enabled: true },
            { name: "Conflict resolution", description: "Auto-resolve merge conflicts", enabled: false },
          ].map((setting) => (
            <div
              key={setting.name}
              className="flex items-center justify-between p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50"
            >
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{setting.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{setting.description}</p>
              </div>
              <button
                className={`w-12 h-6 rounded-full transition-colors ${
                  setting.enabled ? "bg-emerald-500" : "bg-gray-300 dark:bg-gray-600"
                }`}
              >
                <div
                  className={`w-5 h-5 rounded-full bg-white shadow-sm transition-transform ${
                    setting.enabled ? "translate-x-6" : "translate-x-0.5"
                  }`}
                />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderNetworkSection = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Network Configuration</h3>
        <div className="space-y-4">
          <div className="p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50">
            <p className="font-medium text-gray-900 dark:text-white mb-2">Local IP Address</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">*************</p>
          </div>

          <div className="p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50">
            <p className="font-medium text-gray-900 dark:text-white mb-2">Flow Network Name</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">flow.local</p>
          </div>

          <div className="p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50">
            <p className="font-medium text-gray-900 dark:text-white mb-2">Discovery Port</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">8080</p>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl h-[80vh] bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-700 flex">
        {/* Sidebar */}
        <div className="w-64 border-r border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center gap-3 mb-8">
            <div className="w-10 h-10 rounded-2xl bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center">
              <Monitor className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Settings</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Flow Configuration</p>
            </div>
          </div>

          <nav className="space-y-2">
            {sections.map((section) => {
              const Icon = section.icon
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-2xl text-left transition-colors ${
                    activeSection === section.id
                      ? "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300"
                      : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  {section.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              {sections.find((s) => s.id === activeSection)?.label}
            </h3>
            <button
              onClick={onClose}
              className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-6">
            {activeSection === "appearance" && renderAppearanceSection()}
            {activeSection === "ai" && renderAISection()}
            {activeSection === "permissions" && renderPermissionsSection()}
            {activeSection === "sync" && renderSyncSection()}
            {activeSection === "network" && renderNetworkSection()}
          </div>
        </div>
      </div>
    </div>
  )
}
