"use client"

import { useState } from "react"
import { X, AlertTriangle } from "lucide-react"
import { toast } from "./toast"

interface DeleteProjectModalProps {
  project: { id: string; name: string }
  onConfirm: () => void
  onCancel: () => void
}

export default function DeleteProjectModal({ project, onConfirm, onCancel }: DeleteProjectModalProps) {
  const [confirmName, setConfirmName] = useState("")

  const handleConfirm = () => {
    if (confirmName !== project.name) {
      toast.error("Project name doesn't match")
      return
    }
    onConfirm()
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-xl bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Delete Project</h3>
            </div>
            <button
              onClick={onCancel}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          </div>

          <div className="mb-6">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              This action cannot be undone. This will permanently delete the project{" "}
              <span className="font-semibold text-gray-900 dark:text-white">"{project.name}"</span> and all of its data.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mb-4">
              Please type <span className="font-mono font-semibold">{project.name}</span> to confirm.
            </p>
            <input
              type="text"
              value={confirmName}
              onChange={(e) => setConfirmName(e.target.value)}
              placeholder="Enter project name"
              className="w-full px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
            />
          </div>

          <div className="flex items-center justify-end gap-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 rounded-lg border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              disabled={confirmName !== project.name}
              className="px-4 py-2 rounded-lg bg-red-600 text-white font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Delete Project
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
