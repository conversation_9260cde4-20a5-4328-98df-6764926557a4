"use client"

import { useState } from "react"
import { X, GitCom<PERSON>, Clock, User, Tag, ChevronDown, RotateCcw } from "lucide-react"
import { toast } from "./toast"

interface Commit {
  id: string
  title: string
  description: string
  author: string
  timestamp: string
  files: string[]
  tags: string[]
  collaborators: string[]
}

interface CommitPanelProps {
  onClose: () => void
}

const COMMIT_HISTORY: Commit[] = [
  {
    id: "1",
    title: "Add user authentication system",
    description: "Implemented login/signup with JWT tokens and password validation",
    author: "<PERSON>",
    timestamp: "2024-01-15T10:30:00Z",
    files: ["auth.ts", "login.tsx", "signup.tsx"],
    tags: ["feature", "auth"],
    collaborators: ["<PERSON>"],
  },
  {
    id: "2",
    title: "Fix responsive design issues",
    description: "Updated mobile layouts and fixed tablet breakpoints",
    author: "<PERSON>",
    timestamp: "2024-01-14T15:45:00Z",
    files: ["styles.css", "layout.tsx"],
    tags: ["bugfix", "ui"],
    collaborators: [],
  },
  {
    id: "3",
    title: "Add dark mode support",
    description: "Implemented theme switching with system preference detection",
    author: "Mike <PERSON>",
    timestamp: "2024-01-13T09:15:00Z",
    files: ["theme.tsx", "globals.css"],
    tags: ["feature", "ui"],
    collaborators: ["Alex Chen", "Sarah Kim"],
  },
]

const COLLABORATORS = [
  {
    id: "1",
    name: "Alex Chen",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
  },
  {
    id: "2",
    name: "Sarah Kim",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
  },
  {
    id: "3",
    name: "Mike Johnson",
    avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
  },
]

export default function CommitPanel({ onClose }: CommitPanelProps) {
  const [commits, setCommits] = useState(COMMIT_HISTORY)
  const [showCommitModal, setShowCommitModal] = useState(false)
  const [showHistoryDropdown, setShowHistoryDropdown] = useState(false)
  const [commitForm, setCommitForm] = useState({
    title: "",
    description: "",
    tags: [] as string[],
    collaborators: [] as string[],
  })

  const handleCreateCommit = () => {
    if (!commitForm.title.trim()) {
      toast.error("Please enter a commit title")
      return
    }

    const newCommit: Commit = {
      id: Date.now().toString(),
      title: commitForm.title,
      description: commitForm.description,
      author: "Alex Chen",
      timestamp: new Date().toISOString(),
      files: ["src/App.tsx", "src/components/Header.tsx"], // Mock files
      tags: commitForm.tags,
      collaborators: commitForm.collaborators,
    }

    setCommits((prev) => [newCommit, ...prev])
    setShowCommitModal(false)
    setCommitForm({ title: "", description: "", tags: [], collaborators: [] })
    toast.success("Commit created successfully")
  }

  const handleRevertToCommit = (commitId: string) => {
    const commit = commits.find((c) => c.id === commitId)
    if (commit) {
      toast.success(`Reverted to commit: ${commit.title}`)
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  const addTag = (tag: string) => {
    if (tag && !commitForm.tags.includes(tag)) {
      setCommitForm((prev) => ({ ...prev, tags: [...prev.tags, tag] }))
    }
  }

  const removeTag = (tag: string) => {
    setCommitForm((prev) => ({ ...prev, tags: prev.tags.filter((t) => t !== tag) }))
  }

  const toggleCollaborator = (collaboratorId: string) => {
    setCommitForm((prev) => ({
      ...prev,
      collaborators: prev.collaborators.includes(collaboratorId)
        ? prev.collaborators.filter((id) => id !== collaboratorId)
        : [...prev.collaborators, collaboratorId],
    }))
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Version Control</h2>
            <p className="text-gray-600 dark:text-gray-400">Manage local commits and project versions</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative">
              <button
                onClick={() => setShowHistoryDropdown(!showHistoryDropdown)}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <Clock className="w-4 h-4" />
                History
                <ChevronDown className="w-4 h-4" />
              </button>

              {showHistoryDropdown && (
                <div className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 py-2 max-h-64 overflow-y-auto">
                  {commits.slice(0, 5).map((commit) => (
                    <button
                      key={commit.id}
                      onClick={() => {
                        handleRevertToCommit(commit.id)
                        setShowHistoryDropdown(false)
                      }}
                      className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 dark:text-white truncate">{commit.title}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {formatTimestamp(commit.timestamp)}
                          </p>
                        </div>
                        <RotateCcw className="w-4 h-4 text-gray-400 ml-2" />
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
            <button
              onClick={() => setShowCommitModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors"
            >
              <GitCommit className="w-4 h-4" />
              New Commit
            </button>
            <button
              onClick={onClose}
              className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Commit History */}
          <div className="space-y-4">
            {commits.map((commit, index) => (
              <div
                key={commit.id}
                className="relative flex gap-4 p-4 rounded-xl border border-gray-200 dark:border-gray-600 hover:shadow-sm transition-all"
              >
                {/* Timeline */}
                <div className="flex flex-col items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  {index < commits.length - 1 && <div className="w-0.5 h-16 bg-gray-200 dark:bg-gray-600 mt-2"></div>}
                </div>

                {/* Commit Details */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">{commit.title}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{commit.description}</p>
                    </div>
                    <button
                      onClick={() => handleRevertToCommit(commit.id)}
                      className="flex items-center gap-2 px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                    >
                      <RotateCcw className="w-3 h-3" />
                      Revert
                    </button>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      {commit.author}
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      {formatTimestamp(commit.timestamp)}
                    </div>
                    <div className="flex items-center gap-2">
                      <GitCommit className="w-4 h-4" />
                      {commit.files.length} files
                    </div>
                  </div>

                  {/* Tags */}
                  {commit.tags.length > 0 && (
                    <div className="flex items-center gap-2 mb-2">
                      <Tag className="w-4 h-4 text-gray-400" />
                      {commit.tags.map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg text-xs font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}

                  {/* Collaborators */}
                  {commit.collaborators.length > 0 && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Tagged:</span>
                      {commit.collaborators.map((collaboratorName) => (
                        <span
                          key={collaboratorName}
                          className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-lg text-xs font-medium"
                        >
                          @{collaboratorName}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Commit Modal */}
        {showCommitModal && (
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
            <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Create Commit</h3>
                <button
                  onClick={() => setShowCommitModal(false)}
                  className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <X className="w-5 h-5 text-gray-500" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Commit Title *
                  </label>
                  <input
                    type="text"
                    value={commitForm.title}
                    onChange={(e) => setCommitForm((prev) => ({ ...prev, title: e.target.value }))}
                    placeholder="Brief description of changes"
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                  <textarea
                    value={commitForm.description}
                    onChange={(e) => setCommitForm((prev) => ({ ...prev, description: e.target.value }))}
                    placeholder="Detailed description of changes (optional)"
                    rows={3}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors resize-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {commitForm.tags.map((tag) => (
                      <span
                        key={tag}
                        className="flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg text-sm"
                      >
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="w-4 h-4 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full flex items-center justify-center"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    {["feature", "bugfix", "ui", "performance", "docs"].map((tag) => (
                      <button
                        key={tag}
                        onClick={() => addTag(tag)}
                        disabled={commitForm.tags.includes(tag)}
                        className="px-3 py-1 text-sm border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tag Collaborators
                  </label>
                  <div className="space-y-2">
                    {COLLABORATORS.map((collaborator) => (
                      <label
                        key={collaborator.id}
                        className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                      >
                        <input
                          type="checkbox"
                          checked={commitForm.collaborators.includes(collaborator.id)}
                          onChange={() => toggleCollaborator(collaborator.id)}
                          className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500"
                        />
                        <img
                          src={collaborator.avatar || "/placeholder.svg"}
                          alt={collaborator.name}
                          className="w-6 h-6 rounded-full"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{collaborator.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={() => setShowCommitModal(false)}
                    className="flex-1 py-3 px-4 rounded-xl border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateCommit}
                    className="flex-1 py-3 px-4 rounded-xl bg-green-600 text-white hover:bg-green-700 transition-colors"
                  >
                    Create Commit
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
