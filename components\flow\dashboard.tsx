"use client"

import {
  Plus,
  Search,
  Settings,
  Users,
  BarChart3,
  Wifi,
  MessageSquare,
  Share,
  FileText,
  Globe,
  Lock,
  Link,
  Bell,
  Menu,
  MoreVertical,
  Trash2,
  Code,
  Play,
  Pause,
  Square,
  Sparkles,
  Smartphone,
} from "lucide-react"
import { useState } from "react"
import ProjectWorkspace from "./project-workspace"
import ProjectDetails from "./project-details"
import SettingsPage from "./settings-page"
import DevicesPage from "./devices-page"
import AIAssistantPage from "./ai-assistant-page"
import PortSharingPage from "./port-sharing-page"
import AnalyticsPage from "./analytics-page"
import LinkedDevicesPage from "./linked-devices-page"
import ChatPanel from "./chat-panel"
import DeviceChatPanel from "./device-chat-panel"
import NotificationsPanel from "./notifications-panel"
import NewProjectForm from "./new-project-form"
import DeleteProjectModal from "./delete-project-modal"
import { ThemeToggle } from "../theme-toggle"
import { toast } from "./toast"

interface Project {
  id: string
  name: string
  icon: string
  techStack: string[]
  fileCount: number
  visibility: "public" | "private" | "link"
  status: "running" | "paused" | "stopped"
  bannerColor: string
  collaborators: Array<{
    id: string
    name: string
    avatar: string
  }>
  lastEdited: string
  created: string
  description?: string
}

const SAMPLE_PROJECTS: Project[] = [
  {
    id: "1",
    name: "E-commerce Platform",
    icon: "🛍️",
    techStack: ["React", "Node.js", "PostgreSQL", "Tailwind"],
    fileCount: 127,
    visibility: "private",
    status: "running",
    bannerColor: "from-blue-500 to-purple-600",
    collaborators: [
      {
        id: "1",
        name: "Alex Chen",
        avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
      },
      {
        id: "2",
        name: "Sarah Kim",
        avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-02-albo9B0tWOSLXCVZh9rX9KFxXIVWMr.png",
      },
    ],
    lastEdited: "2 hours ago",
    created: "Dec 15, 2024",
    description: "Full-stack e-commerce solution with modern UI and secure payments",
  },
  {
    id: "2",
    name: "Mobile Banking App",
    icon: "💳",
    techStack: ["React Native", "Express", "MongoDB"],
    fileCount: 89,
    visibility: "public",
    status: "paused",
    bannerColor: "from-green-500 to-emerald-600",
    collaborators: [
      {
        id: "1",
        name: "Alex Chen",
        avatar: "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
      },
    ],
    lastEdited: "1 day ago",
    created: "Dec 10, 2024",
    description: "Secure mobile banking application with biometric authentication",
  },
]

type PageType =
  | "dashboard"
  | "devices"
  | "settings"
  | "workspace"
  | "project-details"
  | "ai-assistant"
  | "port-sharing"
  | "analytics"
  | "linked-devices"

export default function Dashboard() {
  const [currentPage, setCurrentPage] = useState<PageType>("dashboard")
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [showChat, setShowChat] = useState(false)
  const [showDeviceChat, setShowDeviceChat] = useState(false)
  const [selectedDevice, setSelectedDevice] = useState<any>(null)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showNewProject, setShowNewProject] = useState(false)
  const [deleteProject, setDeleteProject] = useState<Project | null>(null)
  const [projects, setProjects] = useState(SAMPLE_PROJECTS)
  const [notifications] = useState([
    { id: "1", message: "Sarah Kim joined E-commerce Platform", time: "2 min ago", read: false },
    { id: "2", message: "New device connected: Windows Desktop", time: "5 min ago", read: false },
    { id: "3", message: "Port 3000 is now shared", time: "10 min ago", read: true },
  ])

  const filteredProjects = projects.filter((project) => project.name.toLowerCase().includes(searchQuery.toLowerCase()))

  const unreadNotifications = notifications.filter((n) => !n.read).length

  const handleDeleteProject = (project: Project) => {
    setProjects(projects.filter((p) => p.id !== project.id))
    setDeleteProject(null)
    toast.success(`Project "${project.name}" deleted successfully`)
  }

  const handleCreateProject = (projectData: any) => {
    const newProject: Project = {
      id: Date.now().toString(),
      name: projectData.name,
      icon: projectData.icon,
      techStack: projectData.techStack || [],
      fileCount: 0,
      visibility: projectData.visibility,
      status: "stopped",
      bannerColor: "from-gray-500 to-gray-600",
      collaborators: [
        {
          id: "1",
          name: "Alex Chen",
          avatar:
            "https://ferf1mheo22r9ira.public.blob.vercel-storage.com/avatar-01-n0x8HFv8EUetf9z6ht0wScJKoTHqf8.png",
        },
      ],
      lastEdited: "now",
      created: new Date().toLocaleDateString(),
      description: projectData.description,
    }
    setProjects([...projects, newProject])
    setShowNewProject(false)
  }

  const handleProjectStatusChange = (projectId: string, status: "running" | "paused" | "stopped") => {
    setProjects(projects.map((p) => (p.id === projectId ? { ...p, status } : p)))
    const statusMessages = {
      running: "Project started successfully",
      paused: "Project paused",
      stopped: "Project stopped",
    }
    toast.success(statusMessages[status])
  }

  const handleDeviceChat = (device: any) => {
    setSelectedDevice(device)
    setShowDeviceChat(true)
  }

  const updateProjectBannerColor = (projectId: string, bannerColor: string) => {
    setProjects(projects.map((p) => (p.id === projectId ? { ...p, bannerColor } : p)))
    if (selectedProject?.id === projectId) {
      setSelectedProject({ ...selectedProject, bannerColor })
    }
  }

  if (currentPage === "workspace" && selectedProject) {
    return <ProjectWorkspace project={selectedProject} onBack={() => setCurrentPage("dashboard")} />
  }

  if (currentPage === "project-details" && selectedProject) {
    return (
      <ProjectDetails
        project={selectedProject}
        onBack={() => setCurrentPage("dashboard")}
        onUpdateBannerColor={updateProjectBannerColor}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      {/* Fixed Left Sidebar */}
      <div
        className={`${
          sidebarCollapsed ? "w-20" : "w-80"
        } bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 fixed left-0 top-0 h-full transition-all duration-300 z-40`}
      >
        <div className={`${sidebarCollapsed ? "p-4" : "p-6"} h-full flex flex-col`}>
          {/* Header */}
          <div className={`flex items-center ${sidebarCollapsed ? "justify-center mb-6" : "justify-between mb-8"}`}>
            <div className={`flex items-center gap-3 ${sidebarCollapsed ? "justify-center" : ""}`}>
              <div className="w-10 h-10 rounded-xl bg-green-600 flex items-center justify-center">
                <Wifi className="w-5 h-5 text-white" />
              </div>
              {!sidebarCollapsed && (
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">Flow</h1>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Local Collaboration</p>
                </div>
              )}
            </div>
            {!sidebarCollapsed && (
              <button
                onClick={() => setSidebarCollapsed(true)}
                className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <Menu className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </button>
            )}
          </div>

          {/* Expand button when collapsed */}
          {sidebarCollapsed && (
            <button
              onClick={() => setSidebarCollapsed(false)}
              className="w-full p-3 mb-6 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center"
              title="Expand sidebar"
            >
              <Menu className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
          )}

          {/* Navigation */}
          <nav className="space-y-2 flex-1">
            <NavItem
              icon={FileText}
              label="Projects"
              active={currentPage === "dashboard"}
              collapsed={sidebarCollapsed}
              onClick={() => {
                setCurrentPage("dashboard")
                setShowNewProject(false)
              }}
            />
            <NavItem
              icon={Users}
              label="Devices"
              active={currentPage === "devices"}
              collapsed={sidebarCollapsed}
              onClick={() => setCurrentPage("devices")}
            />
            <NavItem
              icon={Smartphone}
              label="Linked Devices"
              active={currentPage === "linked-devices"}
              collapsed={sidebarCollapsed}
              onClick={() => setCurrentPage("linked-devices")}
            />
            <NavItem
              icon={Sparkles}
              label="AI Assistant"
              active={currentPage === "ai-assistant"}
              collapsed={sidebarCollapsed}
              onClick={() => setCurrentPage("ai-assistant")}
            />
            <NavItem
              icon={Share}
              label="Port Sharing"
              active={currentPage === "port-sharing"}
              collapsed={sidebarCollapsed}
              onClick={() => setCurrentPage("port-sharing")}
            />
            <NavItem
              icon={BarChart3}
              label="Analytics"
              active={currentPage === "analytics"}
              collapsed={sidebarCollapsed}
              onClick={() => setCurrentPage("analytics")}
            />
            <NavItem
              icon={Settings}
              label="Settings"
              active={currentPage === "settings"}
              collapsed={sidebarCollapsed}
              onClick={() => setCurrentPage("settings")}
            />
          </nav>

          {/* Create New Project Button */}
          {!sidebarCollapsed && (
            <div className="mt-8">
              <button
                onClick={() => setShowNewProject(true)}
                className="w-full p-4 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-green-500 dark:hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/10 transition-all group"
              >
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
                    <Plus className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="text-left">
                    <p className="font-medium text-gray-900 dark:text-white">New Project</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Create workspace</p>
                  </div>
                </div>
              </button>
            </div>
          )}

          {/* Collapsed New Project Button */}
          {sidebarCollapsed && (
            <button
              onClick={() => setShowNewProject(true)}
              className="w-full p-3 mb-6 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-green-500 dark:hover:border-green-400 hover:bg-green-50 dark:hover:bg-green-900/10 transition-all flex items-center justify-center"
              title="New Project"
            >
              <Plus className="w-5 h-5 text-green-600 dark:text-green-400" />
            </button>
          )}

          {/* Project Stats */}
          {!sidebarCollapsed && (
            <div className="mt-6 p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="text-gray-600 dark:text-gray-400">Total Projects</span>
                <span className="font-medium text-gray-900 dark:text-white">{projects.length}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Active Today</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {projects.filter((p) => p.status === "running").length}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className={`flex-1 flex flex-col ${sidebarCollapsed ? "ml-20" : "ml-80"} transition-all duration-300`}>
        {/* Fixed Top Navbar */}
        <div
          className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-8 py-4 fixed top-0 right-0 left-0 z-30"
          style={{ left: sidebarCollapsed ? "80px" : "320px" }}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1 max-w-2xl">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
            </div>

            <div className="flex items-center gap-4 ml-6">
              <button
                onClick={() => setShowChat(true)}
                className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <MessageSquare className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </button>

              <button
                onClick={() => setShowNotifications(true)}
                className="relative p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <Bell className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                {unreadNotifications > 0 && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-xs text-white font-medium">{unreadNotifications}</span>
                  </div>
                )}
              </button>

              <ThemeToggle />

              <div className="w-10 h-10 rounded-full bg-green-600 flex items-center justify-center cursor-pointer hover:bg-green-700 transition-colors">
                <span className="text-white font-medium text-sm">AC</span>
              </div>
            </div>
          </div>
        </div>

        {/* Page Content with top margin for fixed navbar */}
        <div className="flex-1 p-8 mt-20 overflow-auto">
          {currentPage === "dashboard" && (
            <div className="max-w-7xl mx-auto">
              <div className="mb-8">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Your Projects</h2>
                <p className="text-gray-600 dark:text-gray-400">Manage and collaborate on your development projects</p>
              </div>

              {/* New Project Form */}
              {showNewProject && (
                <div className="mb-8">
                  <NewProjectForm onSubmit={handleCreateProject} onCancel={() => setShowNewProject(false)} />
                </div>
              )}

              {/* Projects Grid - Hide when creating new project */}
              {!showNewProject && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {filteredProjects.map((project) => (
                    <ProjectCard
                      key={project.id}
                      project={project}
                      onOpen={() => {
                        setSelectedProject(project)
                        setCurrentPage("project-details")
                      }}
                      onWorkspace={() => {
                        setSelectedProject(project)
                        setCurrentPage("workspace")
                      }}
                      onDelete={() => setDeleteProject(project)}
                      onStatusChange={(status) => handleProjectStatusChange(project.id, status)}
                    />
                  ))}
                </div>
              )}

              {!showNewProject && filteredProjects.length === 0 && (
                <div className="text-center py-20">
                  <div className="w-20 h-20 rounded-3xl bg-gray-100 dark:bg-gray-800 flex items-center justify-center mx-auto mb-6">
                    <Search className="w-10 h-10 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">No projects found</h3>
                  <p className="text-gray-500 dark:text-gray-400">Try adjusting your search query</p>
                </div>
              )}
            </div>
          )}

          {currentPage === "devices" && <DevicesPage onDeviceChat={handleDeviceChat} />}
          {currentPage === "linked-devices" && <LinkedDevicesPage />}
          {currentPage === "ai-assistant" && <AIAssistantPage projects={projects} />}
          {currentPage === "port-sharing" && <PortSharingPage />}
          {currentPage === "analytics" && <AnalyticsPage projects={projects} />}
          {currentPage === "settings" && (
            <SettingsPage projects={projects} onUpdateBannerColor={updateProjectBannerColor} />
          )}
        </div>
      </div>

      {/* Modals */}
      {showChat && <ChatPanel onClose={() => setShowChat(false)} />}
      {showDeviceChat && selectedDevice && (
        <DeviceChatPanel device={selectedDevice} onClose={() => setShowDeviceChat(false)} />
      )}
      {showNotifications && (
        <NotificationsPanel notifications={notifications} onClose={() => setShowNotifications(false)} />
      )}
      {deleteProject && (
        <DeleteProjectModal
          project={deleteProject}
          onConfirm={() => handleDeleteProject(deleteProject)}
          onCancel={() => setDeleteProject(null)}
        />
      )}
    </div>
  )
}

// Navigation Item Component
function NavItem({
  icon: Icon,
  label,
  active,
  collapsed,
  onClick,
}: {
  icon: any
  label: string
  active: boolean
  collapsed: boolean
  onClick: () => void
}) {
  return (
    <button
      onClick={onClick}
      className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl text-left transition-colors ${
        active
          ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
          : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
      } ${collapsed ? "justify-center" : ""}`}
      title={collapsed ? label : undefined}
    >
      <Icon className="w-5 h-5" />
      {!collapsed && <span className="font-medium">{label}</span>}
    </button>
  )
}

// Project Card Component
function ProjectCard({
  project,
  onOpen,
  onWorkspace,
  onDelete,
  onStatusChange,
}: {
  project: Project
  onOpen: () => void
  onWorkspace: () => void
  onDelete: () => void
  onStatusChange: (status: "running" | "paused" | "stopped") => void
}) {
  const [showMenu, setShowMenu] = useState(false)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "running":
        return "bg-green-500"
      case "paused":
        return "bg-yellow-500"
      case "stopped":
        return "bg-red-500"
      default:
        return "bg-gray-400"
    }
  }

  return (
    <div className="group bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 hover:shadow-xl hover:border-green-300 dark:hover:border-green-600 transition-all duration-300 overflow-hidden">
      {/* Project Header */}
      <div className="p-8 pb-6">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="text-3xl">{project.icon}</div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                {project.name}
              </h3>
              <div className="flex items-center gap-3 mt-2">
                <div className="flex items-center gap-2">
                  {project.visibility === "public" && <Globe className="w-4 h-4 text-gray-400" />}
                  {project.visibility === "private" && <Lock className="w-4 h-4 text-gray-400" />}
                  {project.visibility === "link" && <Link className="w-4 h-4 text-gray-400" />}
                  <span className="text-sm text-gray-500 dark:text-gray-400 capitalize">{project.visibility}</span>
                </div>
                <span className="text-sm text-gray-400">•</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">{project.fileCount} files</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Status Indicator */}
            <div className="relative">
              <div
                className={`w-4 h-4 rounded-full ${getStatusColor(project.status)} shadow-sm`}
                title={`Status: ${project.status}`}
              />
              <div
                className={`absolute inset-0 w-4 h-4 rounded-full ${getStatusColor(project.status)} animate-ping opacity-20`}
              />
            </div>

            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <MoreVertical className="w-4 h-4 text-gray-400" />
              </button>
              {showMenu && (
                <div className="absolute right-0 top-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-10 min-w-[150px]">
                  <button
                    onClick={() => {
                      onStatusChange("running")
                      setShowMenu(false)
                    }}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left hover:bg-green-50 dark:hover:bg-green-900/20 text-green-600 dark:text-green-400 rounded-xl"
                  >
                    <Play className="w-4 h-4" />
                    Start
                  </button>
                  <button
                    onClick={() => {
                      onStatusChange("paused")
                      setShowMenu(false)
                    }}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left hover:bg-yellow-50 dark:hover:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 rounded-xl"
                  >
                    <Pause className="w-4 h-4" />
                    Pause
                  </button>
                  <button
                    onClick={() => {
                      onStatusChange("stopped")
                      setShowMenu(false)
                    }}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 rounded-xl"
                  >
                    <Square className="w-4 h-4" />
                    Stop
                  </button>
                  <hr className="my-1 border-gray-200 dark:border-gray-600" />
                  <button
                    onClick={() => {
                      onDelete()
                      setShowMenu(false)
                    }}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 rounded-xl"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Tech Stack */}
        <div className="flex flex-wrap gap-2 mb-6">
          {project.techStack.slice(0, 3).map((tech) => (
            <span
              key={tech}
              className="px-3 py-2 text-sm rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
            >
              {tech}
            </span>
          ))}
          {project.techStack.length > 3 && (
            <span className="px-3 py-2 text-sm rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
              +{project.techStack.length - 3}
            </span>
          )}
        </div>

        {/* Collaborators */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex -space-x-3">
              {project.collaborators.slice(0, 3).map((collaborator) => (
                <img
                  key={collaborator.id}
                  src={collaborator.avatar || "/placeholder.svg"}
                  alt={collaborator.name}
                  className="w-8 h-8 rounded-full border-3 border-white dark:border-gray-800"
                />
              ))}
            </div>
            <button className="w-8 h-8 rounded-full border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center hover:border-green-500 dark:hover:border-green-400 transition-colors">
              <Plus className="w-4 h-4 text-gray-400 hover:text-green-500" />
            </button>
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">{project.lastEdited}</span>
        </div>
      </div>

      {/* Project Actions */}
      <div className="px-8 py-6 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-100 dark:border-gray-600">
        <div className="flex gap-3">
          <button
            onClick={onOpen}
            className="flex-1 py-4 px-6 rounded-xl bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
          >
            Open Project
          </button>
          <button
            onClick={onWorkspace}
            className="py-4 px-4 rounded-xl bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
          >
            <Code className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  )
}
