"use client"

import { BarChart3, TrendingUp, Users, Clock, GitBranch, Code, Activity, Zap } from "lucide-react"

interface Project {
  id: string
  name: string
  icon: string
  status: "running" | "paused" | "stopped"
}

interface AnalyticsPageProps {
  projects: Project[]
}

export default function AnalyticsPage({ projects }: AnalyticsPageProps) {
  const stats = {
    totalProjects: projects.length,
    activeProjects: projects.filter((p) => p.status === "running").length,
    totalCollaborators: 8,
    hoursThisWeek: 42,
  }

  const weeklyData = [
    { day: "Mon", commits: 12, hours: 6 },
    { day: "Tue", commits: 8, hours: 4 },
    { day: "Wed", commits: 15, hours: 8 },
    { day: "Thu", commits: 10, hours: 5 },
    { day: "Fri", commits: 18, hours: 9 },
    { day: "Sat", commits: 5, hours: 2 },
    { day: "Sun", commits: 3, hours: 1 },
  ]

  const projectInsights = [
    { name: "E-commerce Platform", commits: 45, contributors: 3, activity: 95 },
    { name: "Mobile Banking App", commits: 32, contributors: 2, activity: 78 },
  ]

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Analytics Dashboard</h2>
        <p className="text-gray-600 dark:text-gray-400">Track your development progress and team productivity</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-blue-600 flex items-center justify-center">
              <Code className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">{stats.totalProjects}</p>
              <p className="text-blue-700 dark:text-blue-300 font-medium">Total Projects</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-2xl p-6 border border-green-200 dark:border-green-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-green-600 flex items-center justify-center">
              <Activity className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-3xl font-bold text-green-900 dark:text-green-100">{stats.activeProjects}</p>
              <p className="text-green-700 dark:text-green-300 font-medium">Active Projects</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-2xl p-6 border border-purple-200 dark:border-purple-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-purple-600 flex items-center justify-center">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">{stats.totalCollaborators}</p>
              <p className="text-purple-700 dark:text-purple-300 font-medium">Collaborators</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 rounded-2xl p-6 border border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-yellow-600 flex items-center justify-center">
              <Clock className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-3xl font-bold text-yellow-900 dark:text-yellow-100">{stats.hoursThisWeek}</p>
              <p className="text-yellow-700 dark:text-yellow-300 font-medium">Hours This Week</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Weekly Activity */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Weekly Activity
          </h3>
          <div className="space-y-4">
            {weeklyData.map((day, index) => (
              <div key={day.day} className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 w-8">{day.day}</span>
                <div className="flex-1 flex gap-2">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-gray-500">Commits</span>
                      <span className="text-xs font-medium text-gray-900 dark:text-white">{day.commits}</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(day.commits / 20) * 100}%` }}
                      />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-gray-500">Hours</span>
                      <span className="text-xs font-medium text-gray-900 dark:text-white">{day.hours}h</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(day.hours / 10) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Project Insights */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Project Insights
          </h3>
          <div className="space-y-6">
            {projectInsights.map((project, index) => (
              <div key={project.name} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">{project.name}</h4>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 mx-auto mb-1">
                      <GitBranch className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">{project.commits}</p>
                    <p className="text-xs text-gray-500">Commits</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30 mx-auto mb-1">
                      <Users className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">{project.contributors}</p>
                    <p className="text-xs text-gray-500">Contributors</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30 mx-auto mb-1">
                      <Zap className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">{project.activity}%</p>
                    <p className="text-xs text-gray-500">Activity</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Development Trends */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Development Trends</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-xl bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
            <div className="w-16 h-16 rounded-2xl bg-green-600 flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-8 h-8 text-white" />
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Productivity</h4>
            <p className="text-3xl font-bold text-green-600 dark:text-green-400 mb-1">+23%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">vs last week</p>
          </div>

          <div className="text-center p-6 rounded-xl bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border border-blue-200 dark:border-blue-800">
            <div className="w-16 h-16 rounded-2xl bg-blue-600 flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-white" />
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Collaboration</h4>
            <p className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">+15%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">team engagement</p>
          </div>

          <div className="text-center p-6 rounded-xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
            <div className="w-16 h-16 rounded-2xl bg-purple-600 flex items-center justify-center mx-auto mb-4">
              <Code className="w-8 h-8 text-white" />
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Code Quality</h4>
            <p className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-1">94%</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">test coverage</p>
          </div>
        </div>
      </div>
    </div>
  )
}
