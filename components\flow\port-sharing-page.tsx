"use client"

import { useState } from "react"
import { Share, Globe, Lock, Plus, X, Copy, ExternalLink, Wifi, Shield, Clock, CheckCircle } from "lucide-react"
import { toast } from "./toast"

interface SharedPort {
  id: string
  port: number
  name: string
  description: string
  url: string
  isPublic: boolean
  isActive: boolean
  createdAt: string
  accessCount: number
  lastAccessed: string
}

export default function PortSharingPage() {
  const [sharedPorts, setSharedPorts] = useState<SharedPort[]>([
    {
      id: "1",
      port: 3000,
      name: "React Development Server",
      description: "Main development server for the e-commerce platform",
      url: "https://flow-3000.local.dev",
      isPublic: true,
      isActive: true,
      createdAt: "2 hours ago",
      accessCount: 24,
      lastAccessed: "5 min ago",
    },
    {
      id: "2",
      port: 8080,
      name: "API Server",
      description: "Backend API for mobile banking app",
      url: "https://flow-8080.local.dev",
      isPublic: false,
      isActive: true,
      createdAt: "1 day ago",
      accessCount: 12,
      lastAccessed: "1 hour ago",
    },
    {
      id: "3",
      port: 5432,
      name: "Database Admin",
      description: "PostgreSQL admin interface",
      url: "https://flow-5432.local.dev",
      isPublic: false,
      isActive: false,
      createdAt: "3 days ago",
      accessCount: 8,
      lastAccessed: "2 days ago",
    },
  ])

  const [showAddPort, setShowAddPort] = useState(false)
  const [newPort, setNewPort] = useState({
    port: "",
    name: "",
    description: "",
    isPublic: false,
  })

  const handleAddPort = () => {
    if (!newPort.port || !newPort.name) {
      toast.error("Port number and name are required")
      return
    }

    const port: SharedPort = {
      id: Date.now().toString(),
      port: Number.parseInt(newPort.port),
      name: newPort.name,
      description: newPort.description,
      url: `https://flow-${newPort.port}.local.dev`,
      isPublic: newPort.isPublic,
      isActive: true,
      createdAt: "now",
      accessCount: 0,
      lastAccessed: "never",
    }

    setSharedPorts([...sharedPorts, port])
    setNewPort({ port: "", name: "", description: "", isPublic: false })
    setShowAddPort(false)
    toast.success(`Port ${newPort.port} is now being shared`)
  }

  const handleTogglePort = (id: string) => {
    setSharedPorts(sharedPorts.map((port) => (port.id === id ? { ...port, isActive: !port.isActive } : port)))
    const port = sharedPorts.find((p) => p.id === id)
    if (port) {
      toast.success(`Port ${port.port} ${port.isActive ? "stopped" : "started"}`)
    }
  }

  const handleDeletePort = (id: string) => {
    const port = sharedPorts.find((p) => p.id === id)
    setSharedPorts(sharedPorts.filter((p) => p.id !== id))
    if (port) {
      toast.success(`Port ${port.port} removed from sharing`)
    }
  }

  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url)
    toast.success("URL copied to clipboard")
  }

  const activePortsCount = sharedPorts.filter((p) => p.isActive).length
  const totalAccessCount = sharedPorts.reduce((sum, port) => sum + port.accessCount, 0)

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Port Sharing</h2>
        <p className="text-gray-600 dark:text-gray-400">
          Share your local development servers with team members and external users
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-200 dark:border-green-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-green-600 flex items-center justify-center">
              <Share className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-3xl font-bold text-green-900 dark:text-green-100">{activePortsCount}</p>
              <p className="text-green-700 dark:text-green-300 font-medium">Active Ports</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-blue-600 flex items-center justify-center">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-3xl font-bold text-blue-900 dark:text-blue-100">{totalAccessCount}</p>
              <p className="text-blue-700 dark:text-blue-300 font-medium">Total Access</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl p-6 border border-purple-200 dark:border-purple-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-xl bg-purple-600 flex items-center justify-center">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <div>
              <p className="text-3xl font-bold text-purple-900 dark:text-purple-100">
                {sharedPorts.filter((p) => !p.isPublic).length}
              </p>
              <p className="text-purple-700 dark:text-purple-300 font-medium">Private Ports</p>
            </div>
          </div>
        </div>
      </div>

      {/* Add Port Button */}
      <div className="mb-8">
        <button
          onClick={() => setShowAddPort(true)}
          className="flex items-center gap-2 px-6 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 text-white font-medium hover:from-green-600 hover:to-emerald-700 transition-all shadow-lg"
        >
          <Plus className="w-5 h-5" />
          Share New Port
        </button>
      </div>

      {/* Add Port Form */}
      {showAddPort && (
        <div className="mb-8 bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Share New Port</h3>
            <button
              onClick={() => setShowAddPort(false)}
              className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Port Number *</label>
              <input
                type="number"
                value={newPort.port}
                onChange={(e) => setNewPort({ ...newPort, port: e.target.value })}
                placeholder="3000"
                className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Service Name *</label>
              <input
                type="text"
                value={newPort.name}
                onChange={(e) => setNewPort({ ...newPort, name: e.target.value })}
                placeholder="React Development Server"
                className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
              <textarea
                value={newPort.description}
                onChange={(e) => setNewPort({ ...newPort, description: e.target.value })}
                placeholder="Brief description of what this service does..."
                rows={3}
                className="w-full px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all resize-none"
              />
            </div>

            <div className="md:col-span-2">
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={newPort.isPublic}
                  onChange={(e) => setNewPort({ ...newPort, isPublic: e.target.checked })}
                  className="w-5 h-5 rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Make this port publicly accessible
                </span>
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-8">
                Public ports can be accessed by anyone with the URL
              </p>
            </div>
          </div>

          <div className="flex gap-3 mt-6">
            <button
              onClick={handleAddPort}
              className="px-6 py-3 rounded-xl bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
            >
              Start Sharing
            </button>
            <button
              onClick={() => setShowAddPort(false)}
              className="px-6 py-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Shared Ports List */}
      <div className="space-y-6">
        {sharedPorts.map((port) => (
          <div
            key={port.id}
            className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-4">
                <div
                  className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    port.isActive ? "bg-green-100 dark:bg-green-900/30" : "bg-gray-100 dark:bg-gray-700"
                  }`}
                >
                  {port.isActive ? (
                    <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                  ) : (
                    <Clock className="w-6 h-6 text-gray-500" />
                  )}
                </div>
                <div>
                  <div className="flex items-center gap-3 mb-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{port.name}</h3>
                    <span className="px-3 py-1 rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium">
                      :{port.port}
                    </span>
                    {port.isPublic ? (
                      <Globe className="w-4 h-4 text-green-500" title="Public" />
                    ) : (
                      <Lock className="w-4 h-4 text-gray-500" title="Private" />
                    )}
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 mb-2">{port.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <span>Created {port.createdAt}</span>
                    <span>•</span>
                    <span>{port.accessCount} accesses</span>
                    <span>•</span>
                    <span>Last accessed {port.lastAccessed}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className={`w-3 h-3 rounded-full ${port.isActive ? "bg-green-500" : "bg-gray-400"}`} />
                <span
                  className={`text-sm font-medium ${
                    port.isActive ? "text-green-600 dark:text-green-400" : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {port.isActive ? "Active" : "Stopped"}
                </span>
              </div>
            </div>

            {/* URL Display */}
            <div className="mb-4 p-4 rounded-xl bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 flex-1">
                  <Wifi className="w-4 h-4 text-gray-500" />
                  <code className="text-sm font-mono text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg border border-gray-200 dark:border-gray-600 flex-1">
                    {port.url}
                  </code>
                </div>
                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => handleCopyUrl(port.url)}
                    className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    title="Copy URL"
                  >
                    <Copy className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                  </button>
                  <button
                    onClick={() => window.open(port.url, "_blank")}
                    className="p-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    title="Open in new tab"
                  >
                    <ExternalLink className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                  </button>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <button
                onClick={() => handleTogglePort(port.id)}
                className={`px-4 py-2 rounded-xl font-medium transition-colors ${
                  port.isActive
                    ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-900/50"
                    : "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50"
                }`}
              >
                {port.isActive ? "Stop Sharing" : "Start Sharing"}
              </button>
              <button
                onClick={() => handleDeletePort(port.id)}
                className="px-4 py-2 rounded-xl bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Remove
              </button>
            </div>
          </div>
        ))}
      </div>

      {sharedPorts.length === 0 && (
        <div className="text-center py-20">
          <div className="w-20 h-20 rounded-3xl bg-gray-100 dark:bg-gray-800 flex items-center justify-center mx-auto mb-6">
            <Share className="w-10 h-10 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">No ports shared yet</h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Start sharing your local development servers with your team
          </p>
          <button
            onClick={() => setShowAddPort(true)}
            className="px-6 py-3 rounded-xl bg-green-600 text-white font-medium hover:bg-green-700 transition-colors"
          >
            Share Your First Port
          </button>
        </div>
      )}
    </div>
  )
}
