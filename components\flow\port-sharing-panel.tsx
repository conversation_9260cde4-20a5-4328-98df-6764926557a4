"use client"

import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Copy, ExternalLink, Play, Square } from "lucide-react"
import { useState } from "react"

interface PortSharingPanelProps {
  onClose: () => void
}

interface SharedPort {
  id: string
  port: number
  name: string
  status: "active" | "inactive"
  url: string
  expiresIn: string
  requests: number
}

export default function PortSharingPanel({ onClose }: PortSharingPanelProps) {
  const [selectedPort, setSelectedPort] = useState("3000")
  const [isSharing, setIsSharing] = useState(false)
  const [sharedPorts, setSharedPorts] = useState<SharedPort[]>([
    {
      id: "1",
      port: 3000,
      name: "React Dev Server",
      status: "active",
      url: "flow.local:3000",
      expiresIn: "2h 15m",
      requests: 47,
    },
  ])

  const commonPorts = [
    { port: "3000", name: "React/Next.js" },
    { port: "5173", name: "Vite" },
    { port: "8080", name: "Development" },
    { port: "4000", name: "Express" },
    { port: "5000", name: "Flask/Node" },
    { port: "8000", name: "Django/FastAPI" },
  ]

  const handleStartSharing = () => {
    const newSharedPort: SharedPort = {
      id: Date.now().toString(),
      port: Number.parseInt(selectedPort),
      name: commonPorts.find((p) => p.port === selectedPort)?.name || "Custom",
      status: "active",
      url: `flow.local:${selectedPort}`,
      expiresIn: "4h 0m",
      requests: 0,
    }

    setSharedPorts([...sharedPorts, newSharedPort])
    setIsSharing(true)
  }

  const handleStopSharing = (portId: string) => {
    setSharedPorts(sharedPorts.filter((p) => p.id !== portId))
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-2xl bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center">
              <Share className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Port Sharing</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">Share your dev servers with team</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* New Port Sharing */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Share New Port</h3>

          <div className="grid grid-cols-3 gap-3 mb-4">
            {commonPorts.map((port) => (
              <button
                key={port.port}
                onClick={() => setSelectedPort(port.port)}
                className={`p-3 rounded-2xl border text-left transition-all ${
                  selectedPort === port.port
                    ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20"
                    : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                }`}
              >
                <div className="font-medium text-gray-900 dark:text-white">:{port.port}</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">{port.name}</div>
              </button>
            ))}
          </div>

          <div className="flex gap-3">
            <input
              type="text"
              value={selectedPort}
              onChange={(e) => setSelectedPort(e.target.value)}
              placeholder="Custom port"
              className="flex-1 px-4 py-3 rounded-2xl border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500"
            />
            <button
              onClick={handleStartSharing}
              className="px-6 py-3 rounded-2xl bg-gradient-to-r from-emerald-500 to-green-600 text-white font-medium hover:from-emerald-600 hover:to-green-700 transition-all shadow-lg flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              Start Sharing
            </button>
          </div>
        </div>

        {/* Active Shared Ports */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Active Shares</h3>

          {sharedPorts.length === 0 ? (
            <div className="text-center py-8">
              <Globe className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500 dark:text-gray-400">No ports currently shared</p>
            </div>
          ) : (
            <div className="space-y-3">
              {sharedPorts.map((port) => (
                <div
                  key={port.id}
                  className="flex items-center justify-between p-4 rounded-2xl bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-xl bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center">
                      <Globe className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">:{port.port}</h4>
                        <span className="px-2 py-1 text-xs rounded-lg bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300">
                          {port.name}
                        </span>
                        <div className="w-2 h-2 rounded-full bg-green-500" />
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{port.url}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-500 mt-1">
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          Expires in {port.expiresIn}
                        </span>
                        <span>{port.requests} requests</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => copyToClipboard(`http://${port.url}`)}
                      className="p-2 rounded-xl bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors"
                    >
                      <Copy className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                    </button>
                    <button
                      onClick={() => window.open(`http://${port.url}`, "_blank")}
                      className="p-2 rounded-xl bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors"
                    >
                      <ExternalLink className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                    </button>
                    <button
                      onClick={() => handleStopSharing(port.id)}
                      className="p-2 rounded-xl bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
                    >
                      <Square className="w-4 h-4 text-red-600 dark:text-red-400" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
