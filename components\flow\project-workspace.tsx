"use client"

import {
  <PERSON>Left,
  Share,
  Users,
  FileText,
  Folder,
  ChevronRight,
  Circle,
  Play,
  GitCommit,
  Bot,
  ChevronLeft,
} from "lucide-react"
import { useState } from "react"
import AIAssistantPanel from "./ai-assistant-panel"
import PortSharingPanel from "./port-sharing-panel"
import FileEditor from "./file-editor"
import CollaboratorPanel from "./collaborator-panel"
import CommitPanel from "./commit-panel"

interface Project {
  id: string
  name: string
  icon: string
  techStack: string[]
  fileCount: number
  visibility: "public" | "private" | "link"
  collaborators: Array<{
    id: string
    name: string
    avatar: string
  }>
  lastEdited: string
  created: string
}

interface ProjectWorkspaceProps {
  project?: Project
  onBack?: () => void
}

interface FileNode {
  name: string
  type: "file" | "folder"
  children?: FileNode[]
  isOpen?: boolean
}

const FILE_TREE: FileNode[] = [
  {
    name: "src",
    type: "folder",
    isOpen: true,
    children: [
      {
        name: "components",
        type: "folder",
        isOpen: true,
        children: [
          { name: "Header.tsx", type: "file" },
          { name: "Sidebar.tsx", type: "file" },
          { name: "Button.tsx", type: "file" },
        ],
      },
      {
        name: "pages",
        type: "folder",
        children: [
          { name: "Home.tsx", type: "file" },
          { name: "About.tsx", type: "file" },
        ],
      },
      { name: "App.tsx", type: "file" },
      { name: "index.tsx", type: "file" },
    ],
  },
  {
    name: "public",
    type: "folder",
    children: [
      { name: "index.html", type: "file" },
      { name: "favicon.ico", type: "file" },
    ],
  },
  { name: "package.json", type: "file" },
  { name: "README.md", type: "file" },
]

const sampleProject: Project = {
  id: "demo",
  name: "Demo Project",
  icon: "🚀",
  techStack: ["Next.js", "TypeScript", "Tailwind"],
  fileCount: 12,
  visibility: "private",
  collaborators: [
    { id: "1", name: "Alice", avatar: "/placeholder.svg?height=24&width=24" },
    { id: "2", name: "Bob", avatar: "/placeholder.svg?height=24&width=24" },
  ],
  lastEdited: "Just now",
  created: "2025-07-06",
}

export default function ProjectWorkspace({ project = sampleProject, onBack = () => {} }: ProjectWorkspaceProps) {
  const [showAI, setShowAI] = useState(false)
  const [showPortSharing, setShowPortSharing] = useState(false)
  const [showCollaborators, setShowCollaborators] = useState(false)
  const [showCommits, setShowCommits] = useState(false)
  const [selectedFile, setSelectedFile] = useState<string>("src/App.tsx")
  const [openTabs, setOpenTabs] = useState<string[]>(["src/App.tsx", "src/components/Header.tsx"])

  const renderFileTree = (nodes: FileNode[], depth = 0) => {
    return nodes.map((node, index) => (
      <div key={`${node.name}-${index}`} className="select-none">
        <div
          className={`flex items-center gap-2 py-2 px-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 cursor-pointer transition-colors ${
            selectedFile === `${depth > 0 ? "src/" : ""}${node.name}` ? "bg-green-100 dark:bg-green-900/30" : ""
          }`}
          style={{ paddingLeft: `${depth * 16 + 12}px` }}
          onClick={() => {
            if (node.type === "file") {
              const filePath = `${depth > 0 ? "src/" : ""}${node.name}`
              setSelectedFile(filePath)
              if (!openTabs.includes(filePath)) {
                setOpenTabs([...openTabs, filePath])
              }
            }
          }}
        >
          {node.type === "folder" ? (
            <>
              <ChevronRight
                className={`w-4 h-4 text-gray-400 transition-transform ${node.isOpen ? "rotate-90" : ""}`}
              />
              <Folder className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </>
          ) : (
            <>
              <div className="w-4" />
              <Circle className="w-3 h-3 text-gray-400" />
            </>
          )}
          <span className="text-sm text-gray-700 dark:text-gray-300">{node.name}</span>
        </div>
        {node.type === "folder" && node.isOpen && node.children && (
          <div>{renderFileTree(node.children, depth + 1)}</div>
        )}
      </div>
    ))
  }

  return (
    <div className="h-screen flex flex-col bg-white dark:bg-gray-900">
      {/* Header */}
      <header className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          </button>
          <div className="flex items-center gap-3">
            <span className="text-2xl">{project.icon}</span>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">{project.name}</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">{project.techStack.join(" • ")}</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowCommits(true)}
            className="flex items-center gap-2 px-4 py-2 rounded-xl bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <GitCommit className="w-4 h-4" />
            Commits
          </button>
          <button className="flex items-center gap-2 px-4 py-2 rounded-xl bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors">
            <Play className="w-4 h-4" />
            Run Dev Server
          </button>
          <button
            onClick={() => setShowPortSharing(true)}
            className="flex items-center gap-2 px-4 py-2 rounded-xl bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <Share className="w-4 h-4" />
            Share Port
          </button>
          <button
            onClick={() => setShowCollaborators(true)}
            className="flex items-center gap-2 px-4 py-2 rounded-xl bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <Users className="w-4 h-4" />
            Collaborators
          </button>

          {/* AI Assistant Toggle Button */}
          <button
            onClick={() => setShowAI(!showAI)}
            className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 ${
              showAI
                ? "bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25"
                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
          >
            <Bot className="w-4 h-4" />
            AI Assistant
            {showAI && <ChevronLeft className="w-4 h-4 ml-1" />}
          </button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* File Tree */}
        <div className="w-80 border-r border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-sm font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Project Files
            </h2>
          </div>
          <div className="p-2 overflow-y-auto">{renderFileTree(FILE_TREE)}</div>
        </div>

        {/* Editor Area */}
        <div className={`flex-1 flex flex-col transition-all duration-300 ease-in-out ${showAI ? "mr-96" : ""}`}>
          {/* Tabs */}
          <div className="flex items-center border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            {openTabs.map((tab) => (
              <div
                key={tab}
                className={`flex items-center gap-2 px-4 py-3 border-r border-gray-200 dark:border-gray-700 cursor-pointer transition-colors ${
                  selectedFile === tab
                    ? "bg-white dark:bg-gray-800 text-green-600 dark:text-green-400"
                    : "bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600"
                }`}
                onClick={() => setSelectedFile(tab)}
              >
                <Circle className="w-3 h-3" />
                <span className="text-sm">{tab.split("/").pop()}</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    const newTabs = openTabs.filter((t) => t !== tab)
                    setOpenTabs(newTabs)
                    if (selectedFile === tab && newTabs.length > 0) {
                      setSelectedFile(newTabs[0])
                    }
                  }}
                  className="w-4 h-4 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center justify-center"
                >
                  ×
                </button>
              </div>
            ))}
          </div>

          {/* Editor */}
          <div className="flex-1">
            <FileEditor fileName={selectedFile} />
          </div>
        </div>

        {/* Collapsible AI Assistant Panel */}
        <div
          className={`fixed right-0 top-[89px] bottom-0 bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 shadow-2xl transition-all duration-300 ease-in-out z-30 ${
            showAI ? "w-96 translate-x-0" : "w-96 translate-x-full"
          }`}
        >
          <AIAssistantPanel onClose={() => setShowAI(false)} isCollapsed={!showAI} />
        </div>
      </div>

      {/* Other Panels */}
      {showPortSharing && <PortSharingPanel onClose={() => setShowPortSharing(false)} />}
      {showCollaborators && <CollaboratorPanel onClose={() => setShowCollaborators(false)} />}
      {showCommits && <CommitPanel onClose={() => setShowCommits(false)} />}
    </div>
  )
}
