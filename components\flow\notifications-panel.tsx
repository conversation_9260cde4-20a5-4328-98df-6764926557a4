"use client"

import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, Wifi, Share } from "lucide-react"

interface Notification {
  id: string
  message: string
  time: string
  read: boolean
  type?: "user" | "device" | "port"
}

interface NotificationsPanelProps {
  notifications: Notification[]
  onClose: () => void
}

export default function NotificationsPanel({ notifications, onClose }: NotificationsPanelProps) {
  const getNotificationIcon = (type?: string) => {
    switch (type) {
      case "user":
        return Users
      case "device":
        return Wifi
      case "port":
        return Share
      default:
        return Bell
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-xl bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                <Bell className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {notifications.filter((n) => !n.read).length} unread
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          </div>

          <div className="space-y-3 max-h-96 overflow-y-auto">
            {notifications.map((notification) => {
              const Icon = getNotificationIcon(notification.type)
              return (
                <div
                  key={notification.id}
                  className={`p-4 rounded-xl border transition-colors ${
                    notification.read
                      ? "border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50"
                      : "border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20"
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div
                      className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                        notification.read ? "bg-gray-200 dark:bg-gray-600" : "bg-green-100 dark:bg-green-900/30"
                      }`}
                    >
                      <Icon
                        className={`w-4 h-4 ${
                          notification.read ? "text-gray-500 dark:text-gray-400" : "text-green-600 dark:text-green-400"
                        }`}
                      />
                    </div>
                    <div className="flex-1">
                      <p
                        className={`text-sm ${
                          notification.read ? "text-gray-600 dark:text-gray-400" : "text-gray-900 dark:text-white"
                        }`}
                      >
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">{notification.time}</p>
                    </div>
                    {!notification.read && (
                      <button className="p-1 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors">
                        <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                      </button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button className="w-full py-2 px-4 rounded-lg text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              Mark all as read
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
