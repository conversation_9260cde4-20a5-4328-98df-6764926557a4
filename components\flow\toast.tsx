"use client"

import { useState, useEffect } from "react"
import { X, CheckCircle, AlertCircle, Info } from "lucide-react"

interface Toast {
  id: string
  type: "success" | "error" | "info"
  message: string
  duration?: number
}

let toastQueue: Toast[] = []
let setToasts: ((toasts: Toast[]) => void) | null = null

export const toast = {
  success: (message: string, duration = 3000) => {
    const id = Date.now().toString()
    const newToast: Toast = { id, type: "success", message, duration }
    toastQueue.push(newToast)
    if (setToasts) {
      setToasts([...toastQueue])
    }
  },
  error: (message: string, duration = 4000) => {
    const id = Date.now().toString()
    const newToast: Toast = { id, type: "error", message, duration }
    toastQueue.push(newToast)
    if (setToasts) {
      setToasts([...toastQueue])
    }
  },
  info: (message: string, duration = 3000) => {
    const id = Date.now().toString()
    const newToast: Toast = { id, type: "info", message, duration }
    toastQueue.push(newToast)
    if (setToasts) {
      setToasts([...toastQueue])
    }
  },
}

export function ToastContainer() {
  const [toasts, setToastsState] = useState<Toast[]>([])

  useEffect(() => {
    setToasts = setToastsState
    return () => {
      setToasts = null
    }
  }, [])

  const removeToast = (id: string) => {
    toastQueue = toastQueue.filter((toast) => toast.id !== id)
    setToastsState([...toastQueue])
  }

  useEffect(() => {
    toasts.forEach((toast) => {
      if (toast.duration) {
        const timer = setTimeout(() => {
          removeToast(toast.id)
        }, toast.duration)
        return () => clearTimeout(timer)
      }
    })
  }, [toasts])

  const getToastIcon = (type: Toast["type"]) => {
    switch (type) {
      case "success":
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case "error":
        return <AlertCircle className="w-5 h-5 text-red-500" />
      case "info":
        return <Info className="w-5 h-5 text-blue-500" />
    }
  }

  const getToastStyles = (type: Toast["type"]) => {
    switch (type) {
      case "success":
        return "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
      case "error":
        return "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
      case "info":
        return "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
    }
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`flex items-center gap-3 p-4 rounded-xl border shadow-lg backdrop-blur-sm transition-all duration-300 ${getToastStyles(
            toast.type,
          )}`}
        >
          {getToastIcon(toast.type)}
          <span className="text-sm font-medium text-gray-900 dark:text-white">{toast.message}</span>
          <button
            onClick={() => removeToast(toast.id)}
            className="p-1 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-4 h-4 text-gray-500" />
          </button>
        </div>
      ))}
    </div>
  )
}
